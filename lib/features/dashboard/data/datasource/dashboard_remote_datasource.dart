import 'package:selfeng/shared/data/remote/remote.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/paginated_response.dart';
import 'package:selfeng/shared/domain/models/product/product_model.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/globals.dart';

abstract class DashboardDatasource {
  Future<Either<AppException, PaginatedResponse>> fetchPaginatedProducts({
    required int skip,
  });
  Future<Either<AppException, PaginatedResponse>> searchPaginatedProducts({
    required int skip,
    required String query,
  });
  Future<Either<AppException, Product>> getDetailProduct({required int id});
}

class DashboardRemoteDatasource extends DashboardDatasource {
  final NetworkService networkService;
  DashboardRemoteDatasource(this.networkService);

  @override
  Future<Either<AppException, PaginatedResponse>> fetchPaginatedProducts({
    required int skip,
  }) async {
    final response = await networkService.get(
      '/products',
      queryParameters: {'skip': skip, 'limit': PRODUCTS_PER_PAGE},
    );

    return response.fold((l) => Left(l), (r) {
      final jsonData = r.data;
      if (jsonData == null) {
        return Left(
          AppException(
            identifier: 'fetchPaginatedData',
            statusCode: 0,
            message: 'The data is not in the valid format.',
          ),
        );
      }
      final paginatedResponse = PaginatedResponse.fromJson(
        jsonData,
        jsonData['products'] ?? [],
      );
      return Right(paginatedResponse);
    });
  }

  @override
  Future<Either<AppException, PaginatedResponse>> searchPaginatedProducts({
    required int skip,
    required String query,
  }) async {
    final response = await networkService.get(
      '/products/search?q=$query',
      queryParameters: {'skip': skip, 'limit': PRODUCTS_PER_PAGE},
    );

    return response.fold((l) => Left(l), (r) {
      final jsonData = r.data;
      if (jsonData == null) {
        return Left(
          AppException(
            identifier: 'search PaginatedData',
            statusCode: 0,
            message: 'The data is not in the valid format.',
          ),
        );
      }
      final paginatedResponse = PaginatedResponse.fromJson(
        jsonData,
        jsonData['products'] ?? [],
      );
      return Right(paginatedResponse);
    });
  }

  @override
  Future<Either<AppException, Product>> getDetailProduct({
    required int id,
  }) async {
    final response = await networkService.get('/products/$id');

    return response.fold((l) => Left(l), (r) {
      final product = Product.fromJson(r.data);

      if (r.data == null) {
        return Left(
          AppException(
            identifier: 'getDetailProduct',
            statusCode: 0,
            message: 'The data is not in the valid format.',
          ),
        );
      }

      return Right(product);
    });
  }
}
