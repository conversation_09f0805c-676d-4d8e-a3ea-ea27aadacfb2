import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/dashboard/data/datasource/dashboard_remote_datasource.dart';
import 'package:selfeng/features/dashboard/data/repositories/dashboard_repository.dart';
import 'package:selfeng/features/dashboard/domain/repositories/dashboard_repository.dart';
import 'package:selfeng/shared/data/remote/network_service.dart';
import 'package:selfeng/shared/domain/providers/dio_network_service_provider.dart';

final dashboardDatasourceProvider =
    Provider.family<DashboardDatasource, NetworkService>(
      (_, networkService) => DashboardRemoteDatasource(networkService),
    );

final dashboardRepositoryProvider = Provider<DashboardRepository>((ref) {
  final networkService = ref.watch(networkServiceProvider);
  final datasource = ref.watch(dashboardDatasourceProvider(networkService));
  final repository = DashboardRepositoryImpl(datasource);

  return repository;
});
