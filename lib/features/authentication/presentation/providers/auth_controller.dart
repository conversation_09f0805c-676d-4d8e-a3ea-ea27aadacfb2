import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/foundation.dart' show kIsWeb, debugPrint;
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/services/user_cache_service/domain/providers/user_cache_provider.dart';
import 'package:selfeng/services/user_cache_service/domain/repositories/user_cache_repository.dart';
import 'package:selfeng/shared/domain/models/user/user_model.dart' as app_user;
import 'package:selfeng/shared/domain/providers/shared_preferences_storage_service_provider.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/features/setting/presentation/providers/notification_settings_provider.dart';

import 'state/auth_state.dart';

part 'auth_controller.g.dart';

@riverpod
class AuthController extends _$AuthController {
  late final UserRepository userRepository;
  late final FirebaseAuth _firebaseAuth;
  late final GoogleSignIn _googleSignIn;

  @override
  Future<AuthState> build() async {
    userRepository = ref.watch(userLocalRepositoryProvider);
    _firebaseAuth = FirebaseAuth.instance;
    _googleSignIn = GoogleSignIn.instance;

    // Clear any potentially corrupted cached data from previous versions
    await _clearCachedAuthData();

    // Initialize GoogleSignIn - required in v7.x
    await _initializeGoogleSignIn();

    // Set up Google Sign-In authentication events listener
    _setupGoogleSignInListener();

    _setupPersistenceListener();

    // await Future.delayed(const Duration(seconds: 3));
    return const AuthState.loading();
  }

  Future<void> logout() async {
    // Unsubscribe from all notification topics before logout
    _cleanupNotificationSubscriptions();

    // Sign out from Firebase Auth
    await _firebaseAuth.signOut();

    // Also disconnect from Google Sign-In to ensure complete logout
    // In v7.x, disconnect() is still available and recommended for complete logout
    await _googleSignIn.disconnect();

    state = const AsyncData(AuthState.signedOut());
  }

  Future<void> loginUser() async {
    try {
      debugPrint(
        'DEBUG: Starting loginUser() - Platform: ${kIsWeb ? 'Web' : 'Mobile'}',
      );

      if (kIsWeb) {
        // For web, the state will be set by _handleWebSignIn
        await _handleWebSignIn();
      } else {
        // For mobile, the state will be set by the authentication event handler
        await _handleMobileSignIn();
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('DEBUG: FirebaseAuthException in loginUser: ${e.message}');
      state = AsyncError(
        UnauthorizedException(e.message ?? 'Authentication failed'),
        StackTrace.current,
      );
    } catch (e) {
      debugPrint('DEBUG: General exception in loginUser: $e');
      state = AsyncError(
        UnauthorizedException('An unexpected error occurred: $e'),
        StackTrace.current,
      );
    }
  }

  Future<void> _handleWebSignIn() async {
    final googleProvider = firebase_auth.GoogleAuthProvider();
    final firebaseUserCredential = await _firebaseAuth.signInWithPopup(
      googleProvider,
    );

    // Get the Firebase user and convert to our User model
    final firebaseUser = firebaseUserCredential.user;
    if (firebaseUser != null) {
      debugPrint('DEBUG: Creating User model from Firebase user (Web)');
      final user = app_user.User(
        id: int.tryParse(firebaseUser.uid.hashCode.toString()) ?? 0,
        username: firebaseUser.displayName ?? '',
        email: firebaseUser.email ?? '',
        firstName: firebaseUser.displayName?.split(' ').first ?? '',
        lastName: firebaseUser.displayName?.split(' ').skip(1).join(' ') ?? '',
        image: firebaseUser.photoURL ?? '',
        token:
            await firebaseUser.getIdToken() ?? '', // Get the Firebase ID token
      );

      debugPrint(
        'DEBUG: Setting state to SignedIn with user: ${user.email} (Web)',
      );
      // Update state to signedIn with user data
      state = AsyncData(AuthState.signedIn(user: user));

      // Initialize notification subscriptions after successful authentication
      _initializeNotificationSubscriptions();
    } else {
      throw Exception(
        'Firebase user is null after successful web authentication',
      );
    }
  }

  Future<void> _handleMobileSignIn() async {
    try {
      // In v7.x, we use authenticate() instead of signIn()
      if (_googleSignIn.supportsAuthenticate()) {
        debugPrint('DEBUG: Starting Google Sign-In authentication...');

        // Add timeout to prevent infinite loading
        await _googleSignIn.authenticate().timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            throw Exception('Google Sign-In authentication timed out');
          },
        );

        debugPrint('DEBUG: Google Sign-In authenticate() completed');
        // The authentication events listener will handle the rest
      } else {
        // For platforms that don't support authenticate (like web),
        // we would need platform-specific handling
        debugPrint('DEBUG: Platform does not support authenticate method');
        throw UnsupportedError('Platform does not support authenticate method');
      }
    } catch (e) {
      debugPrint('DEBUG: Error in _handleMobileSignIn: $e');
      rethrow;
    }
  }

  void _setupGoogleSignInListener() {
    // Listen to Google Sign-In authentication events
    _googleSignIn.authenticationEvents.listen(
      _handleGoogleSignInEvent,
      onError: _handleGoogleSignInError,
    );
  }

  Future<void> _handleGoogleSignInEvent(
    GoogleSignInAuthenticationEvent event,
  ) async {
    try {
      debugPrint('DEBUG: Received Google Sign-In event: ${event.runtimeType}');

      switch (event) {
        case GoogleSignInAuthenticationEventSignIn():
          debugPrint(
            'DEBUG: Processing sign-in event for user: ${event.user.email}',
          );
          final googleAuth = event.user.authentication;

          if (googleAuth.idToken != null) {
            debugPrint('DEBUG: Creating Firebase credential with idToken');
            final credential = firebase_auth.GoogleAuthProvider.credential(
              idToken: googleAuth.idToken,
            );

            debugPrint('DEBUG: Signing in to Firebase...');
            final firebaseUserCredential = await _firebaseAuth
                .signInWithCredential(credential);
            debugPrint('DEBUG: Firebase sign-in completed successfully');

            // Get the Firebase user and convert to our User model
            final firebaseUser = firebaseUserCredential.user;
            if (firebaseUser != null) {
              debugPrint('DEBUG: Creating User model from Firebase user');
              final user = app_user.User(
                id: int.tryParse(firebaseUser.uid.hashCode.toString()) ?? 0,
                username: firebaseUser.displayName ?? '',
                email: firebaseUser.email ?? '',
                firstName: firebaseUser.displayName?.split(' ').first ?? '',
                lastName:
                    firebaseUser.displayName?.split(' ').skip(1).join(' ') ??
                    '',
                image: firebaseUser.photoURL ?? '',
                token:
                    await firebaseUser.getIdToken() ??
                    '', // Get the Firebase ID token
              );

              debugPrint(
                'DEBUG: Setting state to SignedIn with user: ${user.email}',
              );
              // Update state to signedIn with user data
              state = AsyncData(AuthState.signedIn(user: user));

              // Initialize notification subscriptions after successful authentication
              _initializeNotificationSubscriptions();
            } else {
              throw Exception(
                'Firebase user is null after successful authentication',
              );
            }
          } else {
            debugPrint('DEBUG: No idToken available');
            throw Exception('No idToken available from Google Sign-In');
          }
          break;

        case GoogleSignInAuthenticationEventSignOut():
          debugPrint('DEBUG: Processing sign-out event');
          state = const AsyncData(AuthState.signedOut());
          break;
      }
    } catch (e) {
      debugPrint('DEBUG: Error in _handleGoogleSignInEvent: $e');
      state = AsyncError(
        UnauthorizedException('Google Sign-In failed: $e'),
        StackTrace.current,
      );
    }
  }

  void _handleGoogleSignInError(Object error) {
    debugPrint('DEBUG: Google Sign-In stream error: $error');
    state = AsyncError(
      UnauthorizedException('Google Sign-In stream error: $error'),
      StackTrace.current,
    );
  }

  Future<void> _initializeGoogleSignIn() async {
    try {
      debugPrint('DEBUG: Initializing Google Sign-In v7.x...');
      await _googleSignIn.initialize();
      debugPrint('DEBUG: Google Sign-In v7.x initialized successfully');
    } catch (e) {
      debugPrint('DEBUG: Error initializing Google Sign-In v7.x: $e');
      // Try to reinitialize after a brief delay
      await Future.delayed(const Duration(milliseconds: 500));
      try {
        await _googleSignIn.initialize();
        debugPrint(
          'DEBUG: Google Sign-In v7.x initialized successfully on retry',
        );
      } catch (retryError) {
        debugPrint(
          'DEBUG: Failed to initialize Google Sign-In v7.x on retry: $retryError',
        );
        // Don't throw - let the app continue, but authentication might not work
      }
    }
  }

  Future<void> _clearCachedAuthData() async {
    try {
      // Get storage service to check migration flag
      final storageService = ref.read(storageServiceProvider);

      // Check if we've already performed the Google Sign-In v7.x migration
      final hasMigrated = await storageService.has(
        GOOGLE_SIGNIN_V7_MIGRATION_KEY,
      );

      if (hasMigrated) {
        debugPrint(
          'DEBUG: Google Sign-In v7.x migration already completed, skipping cache clear',
        );
        return;
      }

      debugPrint(
        'DEBUG: Performing Google Sign-In v7.x migration - clearing cached authentication data...',
      );

      // Clear local user data that might be corrupted from previous versions
      await userRepository.deleteUser();

      // Sign out from Firebase Auth to clear any cached tokens
      if (_firebaseAuth.currentUser != null) {
        debugPrint('DEBUG: Signing out existing Firebase user');
        await _firebaseAuth.signOut();
      }

      // Disconnect from Google Sign-In to clear any cached Google auth data
      try {
        await _googleSignIn.disconnect();
        debugPrint('DEBUG: Disconnected from Google Sign-In');
      } catch (e) {
        // It's okay if disconnect fails (user might not be signed in)
        debugPrint(
          'DEBUG: Google Sign-In disconnect failed (expected if not signed in): $e',
        );
      }

      // Mark migration as completed
      await storageService.set(GOOGLE_SIGNIN_V7_MIGRATION_KEY, 'true');

      debugPrint('DEBUG: Google Sign-In v7.x migration completed successfully');
    } catch (e) {
      debugPrint('DEBUG: Error during Google Sign-In v7.x migration: $e');
      // Don't throw - we want the app to continue even if migration fails
    }
  }

  void _setupPersistenceListener() {
    listenSelf((_, next) async {
      if (next.isLoading) return;
      if (next.hasError) {
        userRepository.deleteUser();
        return;
      }

      switch (next.requireValue) {
        case SignedIn(:final user):
          await userRepository.saveUser(user: user);
          break;
        case SignedOut():
          await userRepository.deleteUser();
          break;
        case Initial() || Loading() || Failure() || Success():
          break;
      }

      // next.requireValue.map<void>(
      //   signedIn:
      //       (signedIn) async =>
      //           await userRepository.saveUser(user: signedIn.user),
      //   signedOut: (signedOut) async => await userRepository.deleteUser(),
      //   initial: (_) {},
      //   loading: (_) {},
      //   failure: (_) {},
      //   success: (_) {},
      // );
    });
  }

  /// Initialize notification subscriptions after successful authentication
  void _initializeNotificationSubscriptions() {
    try {
      // Initialize notification subscriptions based on user preferences
      // This will subscribe to topics that the user has enabled
      ref.read(notificationSettingsProvider.notifier).initializeSubscriptions();
      debugPrint(
        '✅ Notification subscriptions initialized after authentication',
      );
    } catch (e) {
      debugPrint('❌ Failed to initialize notification subscriptions: $e');
      // Don't throw - notification subscription failure shouldn't break authentication
    }
  }

  /// Cleanup notification subscriptions before logout
  void _cleanupNotificationSubscriptions() {
    try {
      // Disable all notification subscriptions
      ref.read(notificationSettingsProvider.notifier).disableAllNotifications();
      debugPrint('✅ Notification subscriptions cleaned up before logout');
    } catch (e) {
      debugPrint('❌ Failed to cleanup notification subscriptions: $e');
      // Don't throw - notification cleanup failure shouldn't break logout
    }
  }
}

class UnauthorizedException implements Exception {
  const UnauthorizedException(this.message);
  final String message;
}

final networkRoundTripTime = 2.seconds;
