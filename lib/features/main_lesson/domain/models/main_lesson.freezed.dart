// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'main_lesson.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChapterIndexData {

 int get chapter; String get level; String get path;
/// Create a copy of ChapterIndexData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChapterIndexDataCopyWith<ChapterIndexData> get copyWith => _$ChapterIndexDataCopyWithImpl<ChapterIndexData>(this as ChapterIndexData, _$identity);

  /// Serializes this ChapterIndexData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChapterIndexData&&(identical(other.chapter, chapter) || other.chapter == chapter)&&(identical(other.level, level) || other.level == level)&&(identical(other.path, path) || other.path == path));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,chapter,level,path);

@override
String toString() {
  return 'ChapterIndexData(chapter: $chapter, level: $level, path: $path)';
}


}

/// @nodoc
abstract mixin class $ChapterIndexDataCopyWith<$Res>  {
  factory $ChapterIndexDataCopyWith(ChapterIndexData value, $Res Function(ChapterIndexData) _then) = _$ChapterIndexDataCopyWithImpl;
@useResult
$Res call({
 int chapter, String level, String path
});




}
/// @nodoc
class _$ChapterIndexDataCopyWithImpl<$Res>
    implements $ChapterIndexDataCopyWith<$Res> {
  _$ChapterIndexDataCopyWithImpl(this._self, this._then);

  final ChapterIndexData _self;
  final $Res Function(ChapterIndexData) _then;

/// Create a copy of ChapterIndexData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? chapter = null,Object? level = null,Object? path = null,}) {
  return _then(_self.copyWith(
chapter: null == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as int,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as String,path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ChapterIndexData].
extension ChapterIndexDataPatterns on ChapterIndexData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChapterIndexData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChapterIndexData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChapterIndexData value)  $default,){
final _that = this;
switch (_that) {
case _ChapterIndexData():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChapterIndexData value)?  $default,){
final _that = this;
switch (_that) {
case _ChapterIndexData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int chapter,  String level,  String path)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChapterIndexData() when $default != null:
return $default(_that.chapter,_that.level,_that.path);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int chapter,  String level,  String path)  $default,) {final _that = this;
switch (_that) {
case _ChapterIndexData():
return $default(_that.chapter,_that.level,_that.path);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int chapter,  String level,  String path)?  $default,) {final _that = this;
switch (_that) {
case _ChapterIndexData() when $default != null:
return $default(_that.chapter,_that.level,_that.path);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChapterIndexData implements ChapterIndexData {
   _ChapterIndexData({required this.chapter, required this.level, required this.path});
  factory _ChapterIndexData.fromJson(Map<String, dynamic> json) => _$ChapterIndexDataFromJson(json);

@override final  int chapter;
@override final  String level;
@override final  String path;

/// Create a copy of ChapterIndexData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChapterIndexDataCopyWith<_ChapterIndexData> get copyWith => __$ChapterIndexDataCopyWithImpl<_ChapterIndexData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChapterIndexDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChapterIndexData&&(identical(other.chapter, chapter) || other.chapter == chapter)&&(identical(other.level, level) || other.level == level)&&(identical(other.path, path) || other.path == path));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,chapter,level,path);

@override
String toString() {
  return 'ChapterIndexData(chapter: $chapter, level: $level, path: $path)';
}


}

/// @nodoc
abstract mixin class _$ChapterIndexDataCopyWith<$Res> implements $ChapterIndexDataCopyWith<$Res> {
  factory _$ChapterIndexDataCopyWith(_ChapterIndexData value, $Res Function(_ChapterIndexData) _then) = __$ChapterIndexDataCopyWithImpl;
@override @useResult
$Res call({
 int chapter, String level, String path
});




}
/// @nodoc
class __$ChapterIndexDataCopyWithImpl<$Res>
    implements _$ChapterIndexDataCopyWith<$Res> {
  __$ChapterIndexDataCopyWithImpl(this._self, this._then);

  final _ChapterIndexData _self;
  final $Res Function(_ChapterIndexData) _then;

/// Create a copy of ChapterIndexData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? chapter = null,Object? level = null,Object? path = null,}) {
  return _then(_ChapterIndexData(
chapter: null == chapter ? _self.chapter : chapter // ignore: cast_nullable_to_non_nullable
as int,level: null == level ? _self.level : level // ignore: cast_nullable_to_non_nullable
as String,path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$ContentIndexData {

@JsonKey(name: 'content_title') String get title;@JsonKey(name: 'content_path') String get contentPath;@JsonKey(name: 'content_order') int get contentOrder;@JsonKey(name: 'part_order') int? get partOrder;@JsonKey(name: 'part_title') String? get partTitle;@JsonKey(name: 'subpart_order') int? get subpartOrder;@JsonKey(name: 'subpart_title') String? get subpartTitle; bool get hasResult; bool get isBookmarked; bool get firstStage; bool get secondStage; bool get thirdStage;
/// Create a copy of ContentIndexData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ContentIndexDataCopyWith<ContentIndexData> get copyWith => _$ContentIndexDataCopyWithImpl<ContentIndexData>(this as ContentIndexData, _$identity);

  /// Serializes this ContentIndexData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ContentIndexData&&(identical(other.title, title) || other.title == title)&&(identical(other.contentPath, contentPath) || other.contentPath == contentPath)&&(identical(other.contentOrder, contentOrder) || other.contentOrder == contentOrder)&&(identical(other.partOrder, partOrder) || other.partOrder == partOrder)&&(identical(other.partTitle, partTitle) || other.partTitle == partTitle)&&(identical(other.subpartOrder, subpartOrder) || other.subpartOrder == subpartOrder)&&(identical(other.subpartTitle, subpartTitle) || other.subpartTitle == subpartTitle)&&(identical(other.hasResult, hasResult) || other.hasResult == hasResult)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked)&&(identical(other.firstStage, firstStage) || other.firstStage == firstStage)&&(identical(other.secondStage, secondStage) || other.secondStage == secondStage)&&(identical(other.thirdStage, thirdStage) || other.thirdStage == thirdStage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,contentPath,contentOrder,partOrder,partTitle,subpartOrder,subpartTitle,hasResult,isBookmarked,firstStage,secondStage,thirdStage);

@override
String toString() {
  return 'ContentIndexData(title: $title, contentPath: $contentPath, contentOrder: $contentOrder, partOrder: $partOrder, partTitle: $partTitle, subpartOrder: $subpartOrder, subpartTitle: $subpartTitle, hasResult: $hasResult, isBookmarked: $isBookmarked, firstStage: $firstStage, secondStage: $secondStage, thirdStage: $thirdStage)';
}


}

/// @nodoc
abstract mixin class $ContentIndexDataCopyWith<$Res>  {
  factory $ContentIndexDataCopyWith(ContentIndexData value, $Res Function(ContentIndexData) _then) = _$ContentIndexDataCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'content_title') String title,@JsonKey(name: 'content_path') String contentPath,@JsonKey(name: 'content_order') int contentOrder,@JsonKey(name: 'part_order') int? partOrder,@JsonKey(name: 'part_title') String? partTitle,@JsonKey(name: 'subpart_order') int? subpartOrder,@JsonKey(name: 'subpart_title') String? subpartTitle, bool hasResult, bool isBookmarked, bool firstStage, bool secondStage, bool thirdStage
});




}
/// @nodoc
class _$ContentIndexDataCopyWithImpl<$Res>
    implements $ContentIndexDataCopyWith<$Res> {
  _$ContentIndexDataCopyWithImpl(this._self, this._then);

  final ContentIndexData _self;
  final $Res Function(ContentIndexData) _then;

/// Create a copy of ContentIndexData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? title = null,Object? contentPath = null,Object? contentOrder = null,Object? partOrder = freezed,Object? partTitle = freezed,Object? subpartOrder = freezed,Object? subpartTitle = freezed,Object? hasResult = null,Object? isBookmarked = null,Object? firstStage = null,Object? secondStage = null,Object? thirdStage = null,}) {
  return _then(_self.copyWith(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,contentPath: null == contentPath ? _self.contentPath : contentPath // ignore: cast_nullable_to_non_nullable
as String,contentOrder: null == contentOrder ? _self.contentOrder : contentOrder // ignore: cast_nullable_to_non_nullable
as int,partOrder: freezed == partOrder ? _self.partOrder : partOrder // ignore: cast_nullable_to_non_nullable
as int?,partTitle: freezed == partTitle ? _self.partTitle : partTitle // ignore: cast_nullable_to_non_nullable
as String?,subpartOrder: freezed == subpartOrder ? _self.subpartOrder : subpartOrder // ignore: cast_nullable_to_non_nullable
as int?,subpartTitle: freezed == subpartTitle ? _self.subpartTitle : subpartTitle // ignore: cast_nullable_to_non_nullable
as String?,hasResult: null == hasResult ? _self.hasResult : hasResult // ignore: cast_nullable_to_non_nullable
as bool,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,firstStage: null == firstStage ? _self.firstStage : firstStage // ignore: cast_nullable_to_non_nullable
as bool,secondStage: null == secondStage ? _self.secondStage : secondStage // ignore: cast_nullable_to_non_nullable
as bool,thirdStage: null == thirdStage ? _self.thirdStage : thirdStage // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ContentIndexData].
extension ContentIndexDataPatterns on ContentIndexData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ContentIndexData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ContentIndexData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ContentIndexData value)  $default,){
final _that = this;
switch (_that) {
case _ContentIndexData():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ContentIndexData value)?  $default,){
final _that = this;
switch (_that) {
case _ContentIndexData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'content_title')  String title, @JsonKey(name: 'content_path')  String contentPath, @JsonKey(name: 'content_order')  int contentOrder, @JsonKey(name: 'part_order')  int? partOrder, @JsonKey(name: 'part_title')  String? partTitle, @JsonKey(name: 'subpart_order')  int? subpartOrder, @JsonKey(name: 'subpart_title')  String? subpartTitle,  bool hasResult,  bool isBookmarked,  bool firstStage,  bool secondStage,  bool thirdStage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ContentIndexData() when $default != null:
return $default(_that.title,_that.contentPath,_that.contentOrder,_that.partOrder,_that.partTitle,_that.subpartOrder,_that.subpartTitle,_that.hasResult,_that.isBookmarked,_that.firstStage,_that.secondStage,_that.thirdStage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'content_title')  String title, @JsonKey(name: 'content_path')  String contentPath, @JsonKey(name: 'content_order')  int contentOrder, @JsonKey(name: 'part_order')  int? partOrder, @JsonKey(name: 'part_title')  String? partTitle, @JsonKey(name: 'subpart_order')  int? subpartOrder, @JsonKey(name: 'subpart_title')  String? subpartTitle,  bool hasResult,  bool isBookmarked,  bool firstStage,  bool secondStage,  bool thirdStage)  $default,) {final _that = this;
switch (_that) {
case _ContentIndexData():
return $default(_that.title,_that.contentPath,_that.contentOrder,_that.partOrder,_that.partTitle,_that.subpartOrder,_that.subpartTitle,_that.hasResult,_that.isBookmarked,_that.firstStage,_that.secondStage,_that.thirdStage);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'content_title')  String title, @JsonKey(name: 'content_path')  String contentPath, @JsonKey(name: 'content_order')  int contentOrder, @JsonKey(name: 'part_order')  int? partOrder, @JsonKey(name: 'part_title')  String? partTitle, @JsonKey(name: 'subpart_order')  int? subpartOrder, @JsonKey(name: 'subpart_title')  String? subpartTitle,  bool hasResult,  bool isBookmarked,  bool firstStage,  bool secondStage,  bool thirdStage)?  $default,) {final _that = this;
switch (_that) {
case _ContentIndexData() when $default != null:
return $default(_that.title,_that.contentPath,_that.contentOrder,_that.partOrder,_that.partTitle,_that.subpartOrder,_that.subpartTitle,_that.hasResult,_that.isBookmarked,_that.firstStage,_that.secondStage,_that.thirdStage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ContentIndexData implements ContentIndexData {
   _ContentIndexData({@JsonKey(name: 'content_title') this.title = '', @JsonKey(name: 'content_path') this.contentPath = '', @JsonKey(name: 'content_order') this.contentOrder = 1, @JsonKey(name: 'part_order') this.partOrder, @JsonKey(name: 'part_title') this.partTitle, @JsonKey(name: 'subpart_order') this.subpartOrder, @JsonKey(name: 'subpart_title') this.subpartTitle, this.hasResult = false, this.isBookmarked = false, this.firstStage = false, this.secondStage = false, this.thirdStage = false});
  factory _ContentIndexData.fromJson(Map<String, dynamic> json) => _$ContentIndexDataFromJson(json);

@override@JsonKey(name: 'content_title') final  String title;
@override@JsonKey(name: 'content_path') final  String contentPath;
@override@JsonKey(name: 'content_order') final  int contentOrder;
@override@JsonKey(name: 'part_order') final  int? partOrder;
@override@JsonKey(name: 'part_title') final  String? partTitle;
@override@JsonKey(name: 'subpart_order') final  int? subpartOrder;
@override@JsonKey(name: 'subpart_title') final  String? subpartTitle;
@override@JsonKey() final  bool hasResult;
@override@JsonKey() final  bool isBookmarked;
@override@JsonKey() final  bool firstStage;
@override@JsonKey() final  bool secondStage;
@override@JsonKey() final  bool thirdStage;

/// Create a copy of ContentIndexData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ContentIndexDataCopyWith<_ContentIndexData> get copyWith => __$ContentIndexDataCopyWithImpl<_ContentIndexData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ContentIndexDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ContentIndexData&&(identical(other.title, title) || other.title == title)&&(identical(other.contentPath, contentPath) || other.contentPath == contentPath)&&(identical(other.contentOrder, contentOrder) || other.contentOrder == contentOrder)&&(identical(other.partOrder, partOrder) || other.partOrder == partOrder)&&(identical(other.partTitle, partTitle) || other.partTitle == partTitle)&&(identical(other.subpartOrder, subpartOrder) || other.subpartOrder == subpartOrder)&&(identical(other.subpartTitle, subpartTitle) || other.subpartTitle == subpartTitle)&&(identical(other.hasResult, hasResult) || other.hasResult == hasResult)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked)&&(identical(other.firstStage, firstStage) || other.firstStage == firstStage)&&(identical(other.secondStage, secondStage) || other.secondStage == secondStage)&&(identical(other.thirdStage, thirdStage) || other.thirdStage == thirdStage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,contentPath,contentOrder,partOrder,partTitle,subpartOrder,subpartTitle,hasResult,isBookmarked,firstStage,secondStage,thirdStage);

@override
String toString() {
  return 'ContentIndexData(title: $title, contentPath: $contentPath, contentOrder: $contentOrder, partOrder: $partOrder, partTitle: $partTitle, subpartOrder: $subpartOrder, subpartTitle: $subpartTitle, hasResult: $hasResult, isBookmarked: $isBookmarked, firstStage: $firstStage, secondStage: $secondStage, thirdStage: $thirdStage)';
}


}

/// @nodoc
abstract mixin class _$ContentIndexDataCopyWith<$Res> implements $ContentIndexDataCopyWith<$Res> {
  factory _$ContentIndexDataCopyWith(_ContentIndexData value, $Res Function(_ContentIndexData) _then) = __$ContentIndexDataCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'content_title') String title,@JsonKey(name: 'content_path') String contentPath,@JsonKey(name: 'content_order') int contentOrder,@JsonKey(name: 'part_order') int? partOrder,@JsonKey(name: 'part_title') String? partTitle,@JsonKey(name: 'subpart_order') int? subpartOrder,@JsonKey(name: 'subpart_title') String? subpartTitle, bool hasResult, bool isBookmarked, bool firstStage, bool secondStage, bool thirdStage
});




}
/// @nodoc
class __$ContentIndexDataCopyWithImpl<$Res>
    implements _$ContentIndexDataCopyWith<$Res> {
  __$ContentIndexDataCopyWithImpl(this._self, this._then);

  final _ContentIndexData _self;
  final $Res Function(_ContentIndexData) _then;

/// Create a copy of ContentIndexData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? title = null,Object? contentPath = null,Object? contentOrder = null,Object? partOrder = freezed,Object? partTitle = freezed,Object? subpartOrder = freezed,Object? subpartTitle = freezed,Object? hasResult = null,Object? isBookmarked = null,Object? firstStage = null,Object? secondStage = null,Object? thirdStage = null,}) {
  return _then(_ContentIndexData(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,contentPath: null == contentPath ? _self.contentPath : contentPath // ignore: cast_nullable_to_non_nullable
as String,contentOrder: null == contentOrder ? _self.contentOrder : contentOrder // ignore: cast_nullable_to_non_nullable
as int,partOrder: freezed == partOrder ? _self.partOrder : partOrder // ignore: cast_nullable_to_non_nullable
as int?,partTitle: freezed == partTitle ? _self.partTitle : partTitle // ignore: cast_nullable_to_non_nullable
as String?,subpartOrder: freezed == subpartOrder ? _self.subpartOrder : subpartOrder // ignore: cast_nullable_to_non_nullable
as int?,subpartTitle: freezed == subpartTitle ? _self.subpartTitle : subpartTitle // ignore: cast_nullable_to_non_nullable
as String?,hasResult: null == hasResult ? _self.hasResult : hasResult // ignore: cast_nullable_to_non_nullable
as bool,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,firstStage: null == firstStage ? _self.firstStage : firstStage // ignore: cast_nullable_to_non_nullable
as bool,secondStage: null == secondStage ? _self.secondStage : secondStage // ignore: cast_nullable_to_non_nullable
as bool,thirdStage: null == thirdStage ? _self.thirdStage : thirdStage // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$MainLesson {

@JsonKey(includeToJson: false) QuestionConcreteState get state;@JsonKey(name: 'question_id') String? get questionId;@JsonKey(includeToJson: false) int get order;@JsonKey(name: 'correct_answer', includeToJson: false) String get correctAnswer;@JsonKey(includeToJson: false) String get question; String get answer;@JsonKey(name: 'is_correct') bool get isCorrect;
/// Create a copy of MainLesson
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MainLessonCopyWith<MainLesson> get copyWith => _$MainLessonCopyWithImpl<MainLesson>(this as MainLesson, _$identity);

  /// Serializes this MainLesson to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MainLesson&&(identical(other.state, state) || other.state == state)&&(identical(other.questionId, questionId) || other.questionId == questionId)&&(identical(other.order, order) || other.order == order)&&(identical(other.correctAnswer, correctAnswer) || other.correctAnswer == correctAnswer)&&(identical(other.question, question) || other.question == question)&&(identical(other.answer, answer) || other.answer == answer)&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,state,questionId,order,correctAnswer,question,answer,isCorrect);

@override
String toString() {
  return 'MainLesson(state: $state, questionId: $questionId, order: $order, correctAnswer: $correctAnswer, question: $question, answer: $answer, isCorrect: $isCorrect)';
}


}

/// @nodoc
abstract mixin class $MainLessonCopyWith<$Res>  {
  factory $MainLessonCopyWith(MainLesson value, $Res Function(MainLesson) _then) = _$MainLessonCopyWithImpl;
@useResult
$Res call({
@JsonKey(includeToJson: false) QuestionConcreteState state,@JsonKey(name: 'question_id') String? questionId,@JsonKey(includeToJson: false) int order,@JsonKey(name: 'correct_answer', includeToJson: false) String correctAnswer,@JsonKey(includeToJson: false) String question, String answer,@JsonKey(name: 'is_correct') bool isCorrect
});




}
/// @nodoc
class _$MainLessonCopyWithImpl<$Res>
    implements $MainLessonCopyWith<$Res> {
  _$MainLessonCopyWithImpl(this._self, this._then);

  final MainLesson _self;
  final $Res Function(MainLesson) _then;

/// Create a copy of MainLesson
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? state = null,Object? questionId = freezed,Object? order = null,Object? correctAnswer = null,Object? question = null,Object? answer = null,Object? isCorrect = null,}) {
  return _then(_self.copyWith(
state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as QuestionConcreteState,questionId: freezed == questionId ? _self.questionId : questionId // ignore: cast_nullable_to_non_nullable
as String?,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,correctAnswer: null == correctAnswer ? _self.correctAnswer : correctAnswer // ignore: cast_nullable_to_non_nullable
as String,question: null == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as String,answer: null == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as String,isCorrect: null == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [MainLesson].
extension MainLessonPatterns on MainLesson {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MainLesson value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MainLesson() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MainLesson value)  $default,){
final _that = this;
switch (_that) {
case _MainLesson():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MainLesson value)?  $default,){
final _that = this;
switch (_that) {
case _MainLesson() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(includeToJson: false)  QuestionConcreteState state, @JsonKey(name: 'question_id')  String? questionId, @JsonKey(includeToJson: false)  int order, @JsonKey(name: 'correct_answer', includeToJson: false)  String correctAnswer, @JsonKey(includeToJson: false)  String question,  String answer, @JsonKey(name: 'is_correct')  bool isCorrect)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MainLesson() when $default != null:
return $default(_that.state,_that.questionId,_that.order,_that.correctAnswer,_that.question,_that.answer,_that.isCorrect);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(includeToJson: false)  QuestionConcreteState state, @JsonKey(name: 'question_id')  String? questionId, @JsonKey(includeToJson: false)  int order, @JsonKey(name: 'correct_answer', includeToJson: false)  String correctAnswer, @JsonKey(includeToJson: false)  String question,  String answer, @JsonKey(name: 'is_correct')  bool isCorrect)  $default,) {final _that = this;
switch (_that) {
case _MainLesson():
return $default(_that.state,_that.questionId,_that.order,_that.correctAnswer,_that.question,_that.answer,_that.isCorrect);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(includeToJson: false)  QuestionConcreteState state, @JsonKey(name: 'question_id')  String? questionId, @JsonKey(includeToJson: false)  int order, @JsonKey(name: 'correct_answer', includeToJson: false)  String correctAnswer, @JsonKey(includeToJson: false)  String question,  String answer, @JsonKey(name: 'is_correct')  bool isCorrect)?  $default,) {final _that = this;
switch (_that) {
case _MainLesson() when $default != null:
return $default(_that.state,_that.questionId,_that.order,_that.correctAnswer,_that.question,_that.answer,_that.isCorrect);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MainLesson implements MainLesson {
   _MainLesson({@JsonKey(includeToJson: false) this.state = QuestionConcreteState.initial, @JsonKey(name: 'question_id') this.questionId, @JsonKey(includeToJson: false) this.order = 0, @JsonKey(name: 'correct_answer', includeToJson: false) this.correctAnswer = '', @JsonKey(includeToJson: false) this.question = '', this.answer = '', @JsonKey(name: 'is_correct') this.isCorrect = false});
  factory _MainLesson.fromJson(Map<String, dynamic> json) => _$MainLessonFromJson(json);

@override@JsonKey(includeToJson: false) final  QuestionConcreteState state;
@override@JsonKey(name: 'question_id') final  String? questionId;
@override@JsonKey(includeToJson: false) final  int order;
@override@JsonKey(name: 'correct_answer', includeToJson: false) final  String correctAnswer;
@override@JsonKey(includeToJson: false) final  String question;
@override@JsonKey() final  String answer;
@override@JsonKey(name: 'is_correct') final  bool isCorrect;

/// Create a copy of MainLesson
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MainLessonCopyWith<_MainLesson> get copyWith => __$MainLessonCopyWithImpl<_MainLesson>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MainLessonToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MainLesson&&(identical(other.state, state) || other.state == state)&&(identical(other.questionId, questionId) || other.questionId == questionId)&&(identical(other.order, order) || other.order == order)&&(identical(other.correctAnswer, correctAnswer) || other.correctAnswer == correctAnswer)&&(identical(other.question, question) || other.question == question)&&(identical(other.answer, answer) || other.answer == answer)&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,state,questionId,order,correctAnswer,question,answer,isCorrect);

@override
String toString() {
  return 'MainLesson(state: $state, questionId: $questionId, order: $order, correctAnswer: $correctAnswer, question: $question, answer: $answer, isCorrect: $isCorrect)';
}


}

/// @nodoc
abstract mixin class _$MainLessonCopyWith<$Res> implements $MainLessonCopyWith<$Res> {
  factory _$MainLessonCopyWith(_MainLesson value, $Res Function(_MainLesson) _then) = __$MainLessonCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(includeToJson: false) QuestionConcreteState state,@JsonKey(name: 'question_id') String? questionId,@JsonKey(includeToJson: false) int order,@JsonKey(name: 'correct_answer', includeToJson: false) String correctAnswer,@JsonKey(includeToJson: false) String question, String answer,@JsonKey(name: 'is_correct') bool isCorrect
});




}
/// @nodoc
class __$MainLessonCopyWithImpl<$Res>
    implements _$MainLessonCopyWith<$Res> {
  __$MainLessonCopyWithImpl(this._self, this._then);

  final _MainLesson _self;
  final $Res Function(_MainLesson) _then;

/// Create a copy of MainLesson
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? state = null,Object? questionId = freezed,Object? order = null,Object? correctAnswer = null,Object? question = null,Object? answer = null,Object? isCorrect = null,}) {
  return _then(_MainLesson(
state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as QuestionConcreteState,questionId: freezed == questionId ? _self.questionId : questionId // ignore: cast_nullable_to_non_nullable
as String?,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,correctAnswer: null == correctAnswer ? _self.correctAnswer : correctAnswer // ignore: cast_nullable_to_non_nullable
as String,question: null == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as String,answer: null == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as String,isCorrect: null == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$PronunciationSubPart {

@JsonKey(name: "audio_url") String get audio;@JsonKey(name: "image_url") String get image;@JsonKey(name: "caption") String get caption;@JsonKey(name: "translation") String get translation; String? get description;@JsonKey(name: "order") int get order; bool get isBookmarked;
/// Create a copy of PronunciationSubPart
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PronunciationSubPartCopyWith<PronunciationSubPart> get copyWith => _$PronunciationSubPartCopyWithImpl<PronunciationSubPart>(this as PronunciationSubPart, _$identity);

  /// Serializes this PronunciationSubPart to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PronunciationSubPart&&(identical(other.audio, audio) || other.audio == audio)&&(identical(other.image, image) || other.image == image)&&(identical(other.caption, caption) || other.caption == caption)&&(identical(other.translation, translation) || other.translation == translation)&&(identical(other.description, description) || other.description == description)&&(identical(other.order, order) || other.order == order)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,audio,image,caption,translation,description,order,isBookmarked);

@override
String toString() {
  return 'PronunciationSubPart(audio: $audio, image: $image, caption: $caption, translation: $translation, description: $description, order: $order, isBookmarked: $isBookmarked)';
}


}

/// @nodoc
abstract mixin class $PronunciationSubPartCopyWith<$Res>  {
  factory $PronunciationSubPartCopyWith(PronunciationSubPart value, $Res Function(PronunciationSubPart) _then) = _$PronunciationSubPartCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "audio_url") String audio,@JsonKey(name: "image_url") String image,@JsonKey(name: "caption") String caption,@JsonKey(name: "translation") String translation, String? description,@JsonKey(name: "order") int order, bool isBookmarked
});




}
/// @nodoc
class _$PronunciationSubPartCopyWithImpl<$Res>
    implements $PronunciationSubPartCopyWith<$Res> {
  _$PronunciationSubPartCopyWithImpl(this._self, this._then);

  final PronunciationSubPart _self;
  final $Res Function(PronunciationSubPart) _then;

/// Create a copy of PronunciationSubPart
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? audio = null,Object? image = null,Object? caption = null,Object? translation = null,Object? description = freezed,Object? order = null,Object? isBookmarked = null,}) {
  return _then(_self.copyWith(
audio: null == audio ? _self.audio : audio // ignore: cast_nullable_to_non_nullable
as String,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,caption: null == caption ? _self.caption : caption // ignore: cast_nullable_to_non_nullable
as String,translation: null == translation ? _self.translation : translation // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PronunciationSubPart].
extension PronunciationSubPartPatterns on PronunciationSubPart {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PronunciationSubPart value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PronunciationSubPart() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PronunciationSubPart value)  $default,){
final _that = this;
switch (_that) {
case _PronunciationSubPart():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PronunciationSubPart value)?  $default,){
final _that = this;
switch (_that) {
case _PronunciationSubPart() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "audio_url")  String audio, @JsonKey(name: "image_url")  String image, @JsonKey(name: "caption")  String caption, @JsonKey(name: "translation")  String translation,  String? description, @JsonKey(name: "order")  int order,  bool isBookmarked)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PronunciationSubPart() when $default != null:
return $default(_that.audio,_that.image,_that.caption,_that.translation,_that.description,_that.order,_that.isBookmarked);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "audio_url")  String audio, @JsonKey(name: "image_url")  String image, @JsonKey(name: "caption")  String caption, @JsonKey(name: "translation")  String translation,  String? description, @JsonKey(name: "order")  int order,  bool isBookmarked)  $default,) {final _that = this;
switch (_that) {
case _PronunciationSubPart():
return $default(_that.audio,_that.image,_that.caption,_that.translation,_that.description,_that.order,_that.isBookmarked);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "audio_url")  String audio, @JsonKey(name: "image_url")  String image, @JsonKey(name: "caption")  String caption, @JsonKey(name: "translation")  String translation,  String? description, @JsonKey(name: "order")  int order,  bool isBookmarked)?  $default,) {final _that = this;
switch (_that) {
case _PronunciationSubPart() when $default != null:
return $default(_that.audio,_that.image,_that.caption,_that.translation,_that.description,_that.order,_that.isBookmarked);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PronunciationSubPart implements PronunciationSubPart {
  const _PronunciationSubPart({@JsonKey(name: "audio_url") required this.audio, @JsonKey(name: "image_url") required this.image, @JsonKey(name: "caption") required this.caption, @JsonKey(name: "translation") required this.translation, this.description, @JsonKey(name: "order") required this.order, this.isBookmarked = false});
  factory _PronunciationSubPart.fromJson(Map<String, dynamic> json) => _$PronunciationSubPartFromJson(json);

@override@JsonKey(name: "audio_url") final  String audio;
@override@JsonKey(name: "image_url") final  String image;
@override@JsonKey(name: "caption") final  String caption;
@override@JsonKey(name: "translation") final  String translation;
@override final  String? description;
@override@JsonKey(name: "order") final  int order;
@override@JsonKey() final  bool isBookmarked;

/// Create a copy of PronunciationSubPart
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PronunciationSubPartCopyWith<_PronunciationSubPart> get copyWith => __$PronunciationSubPartCopyWithImpl<_PronunciationSubPart>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PronunciationSubPartToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PronunciationSubPart&&(identical(other.audio, audio) || other.audio == audio)&&(identical(other.image, image) || other.image == image)&&(identical(other.caption, caption) || other.caption == caption)&&(identical(other.translation, translation) || other.translation == translation)&&(identical(other.description, description) || other.description == description)&&(identical(other.order, order) || other.order == order)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,audio,image,caption,translation,description,order,isBookmarked);

@override
String toString() {
  return 'PronunciationSubPart(audio: $audio, image: $image, caption: $caption, translation: $translation, description: $description, order: $order, isBookmarked: $isBookmarked)';
}


}

/// @nodoc
abstract mixin class _$PronunciationSubPartCopyWith<$Res> implements $PronunciationSubPartCopyWith<$Res> {
  factory _$PronunciationSubPartCopyWith(_PronunciationSubPart value, $Res Function(_PronunciationSubPart) _then) = __$PronunciationSubPartCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "audio_url") String audio,@JsonKey(name: "image_url") String image,@JsonKey(name: "caption") String caption,@JsonKey(name: "translation") String translation, String? description,@JsonKey(name: "order") int order, bool isBookmarked
});




}
/// @nodoc
class __$PronunciationSubPartCopyWithImpl<$Res>
    implements _$PronunciationSubPartCopyWith<$Res> {
  __$PronunciationSubPartCopyWithImpl(this._self, this._then);

  final _PronunciationSubPart _self;
  final $Res Function(_PronunciationSubPart) _then;

/// Create a copy of PronunciationSubPart
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? audio = null,Object? image = null,Object? caption = null,Object? translation = null,Object? description = freezed,Object? order = null,Object? isBookmarked = null,}) {
  return _then(_PronunciationSubPart(
audio: null == audio ? _self.audio : audio // ignore: cast_nullable_to_non_nullable
as String,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,caption: null == caption ? _self.caption : caption // ignore: cast_nullable_to_non_nullable
as String,translation: null == translation ? _self.translation : translation // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$ConversationPart {

@JsonKey(name: "order") int? get order;@JsonKey(name: "title") String? get title;@JsonKey(name: "video_url") String? get video;@JsonKey(name: "video_controller", fromJson: videoControllerFromJson) dynamic get videoController;@JsonKey(name: "video_meta") VideoMeta? get videoMeta;@JsonKey(name: "img_url") String? get image; bool get isBookmarked;
/// Create a copy of ConversationPart
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConversationPartCopyWith<ConversationPart> get copyWith => _$ConversationPartCopyWithImpl<ConversationPart>(this as ConversationPart, _$identity);

  /// Serializes this ConversationPart to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ConversationPart&&(identical(other.order, order) || other.order == order)&&(identical(other.title, title) || other.title == title)&&(identical(other.video, video) || other.video == video)&&const DeepCollectionEquality().equals(other.videoController, videoController)&&(identical(other.videoMeta, videoMeta) || other.videoMeta == videoMeta)&&(identical(other.image, image) || other.image == image)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,order,title,video,const DeepCollectionEquality().hash(videoController),videoMeta,image,isBookmarked);

@override
String toString() {
  return 'ConversationPart(order: $order, title: $title, video: $video, videoController: $videoController, videoMeta: $videoMeta, image: $image, isBookmarked: $isBookmarked)';
}


}

/// @nodoc
abstract mixin class $ConversationPartCopyWith<$Res>  {
  factory $ConversationPartCopyWith(ConversationPart value, $Res Function(ConversationPart) _then) = _$ConversationPartCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "order") int? order,@JsonKey(name: "title") String? title,@JsonKey(name: "video_url") String? video,@JsonKey(name: "video_controller", fromJson: videoControllerFromJson) dynamic videoController,@JsonKey(name: "video_meta") VideoMeta? videoMeta,@JsonKey(name: "img_url") String? image, bool isBookmarked
});


$VideoMetaCopyWith<$Res>? get videoMeta;

}
/// @nodoc
class _$ConversationPartCopyWithImpl<$Res>
    implements $ConversationPartCopyWith<$Res> {
  _$ConversationPartCopyWithImpl(this._self, this._then);

  final ConversationPart _self;
  final $Res Function(ConversationPart) _then;

/// Create a copy of ConversationPart
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? order = freezed,Object? title = freezed,Object? video = freezed,Object? videoController = freezed,Object? videoMeta = freezed,Object? image = freezed,Object? isBookmarked = null,}) {
  return _then(_self.copyWith(
order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,video: freezed == video ? _self.video : video // ignore: cast_nullable_to_non_nullable
as String?,videoController: freezed == videoController ? _self.videoController : videoController // ignore: cast_nullable_to_non_nullable
as dynamic,videoMeta: freezed == videoMeta ? _self.videoMeta : videoMeta // ignore: cast_nullable_to_non_nullable
as VideoMeta?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of ConversationPart
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VideoMetaCopyWith<$Res>? get videoMeta {
    if (_self.videoMeta == null) {
    return null;
  }

  return $VideoMetaCopyWith<$Res>(_self.videoMeta!, (value) {
    return _then(_self.copyWith(videoMeta: value));
  });
}
}


/// Adds pattern-matching-related methods to [ConversationPart].
extension ConversationPartPatterns on ConversationPart {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ConversationPart value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ConversationPart() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ConversationPart value)  $default,){
final _that = this;
switch (_that) {
case _ConversationPart():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ConversationPart value)?  $default,){
final _that = this;
switch (_that) {
case _ConversationPart() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "order")  int? order, @JsonKey(name: "title")  String? title, @JsonKey(name: "video_url")  String? video, @JsonKey(name: "video_controller", fromJson: videoControllerFromJson)  dynamic videoController, @JsonKey(name: "video_meta")  VideoMeta? videoMeta, @JsonKey(name: "img_url")  String? image,  bool isBookmarked)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ConversationPart() when $default != null:
return $default(_that.order,_that.title,_that.video,_that.videoController,_that.videoMeta,_that.image,_that.isBookmarked);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "order")  int? order, @JsonKey(name: "title")  String? title, @JsonKey(name: "video_url")  String? video, @JsonKey(name: "video_controller", fromJson: videoControllerFromJson)  dynamic videoController, @JsonKey(name: "video_meta")  VideoMeta? videoMeta, @JsonKey(name: "img_url")  String? image,  bool isBookmarked)  $default,) {final _that = this;
switch (_that) {
case _ConversationPart():
return $default(_that.order,_that.title,_that.video,_that.videoController,_that.videoMeta,_that.image,_that.isBookmarked);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "order")  int? order, @JsonKey(name: "title")  String? title, @JsonKey(name: "video_url")  String? video, @JsonKey(name: "video_controller", fromJson: videoControllerFromJson)  dynamic videoController, @JsonKey(name: "video_meta")  VideoMeta? videoMeta, @JsonKey(name: "img_url")  String? image,  bool isBookmarked)?  $default,) {final _that = this;
switch (_that) {
case _ConversationPart() when $default != null:
return $default(_that.order,_that.title,_that.video,_that.videoController,_that.videoMeta,_that.image,_that.isBookmarked);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ConversationPart implements ConversationPart {
  const _ConversationPart({@JsonKey(name: "order") this.order, @JsonKey(name: "title") this.title, @JsonKey(name: "video_url") this.video, @JsonKey(name: "video_controller", fromJson: videoControllerFromJson) this.videoController, @JsonKey(name: "video_meta") this.videoMeta, @JsonKey(name: "img_url") this.image, this.isBookmarked = false});
  factory _ConversationPart.fromJson(Map<String, dynamic> json) => _$ConversationPartFromJson(json);

@override@JsonKey(name: "order") final  int? order;
@override@JsonKey(name: "title") final  String? title;
@override@JsonKey(name: "video_url") final  String? video;
@override@JsonKey(name: "video_controller", fromJson: videoControllerFromJson) final  dynamic videoController;
@override@JsonKey(name: "video_meta") final  VideoMeta? videoMeta;
@override@JsonKey(name: "img_url") final  String? image;
@override@JsonKey() final  bool isBookmarked;

/// Create a copy of ConversationPart
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ConversationPartCopyWith<_ConversationPart> get copyWith => __$ConversationPartCopyWithImpl<_ConversationPart>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ConversationPartToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ConversationPart&&(identical(other.order, order) || other.order == order)&&(identical(other.title, title) || other.title == title)&&(identical(other.video, video) || other.video == video)&&const DeepCollectionEquality().equals(other.videoController, videoController)&&(identical(other.videoMeta, videoMeta) || other.videoMeta == videoMeta)&&(identical(other.image, image) || other.image == image)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,order,title,video,const DeepCollectionEquality().hash(videoController),videoMeta,image,isBookmarked);

@override
String toString() {
  return 'ConversationPart(order: $order, title: $title, video: $video, videoController: $videoController, videoMeta: $videoMeta, image: $image, isBookmarked: $isBookmarked)';
}


}

/// @nodoc
abstract mixin class _$ConversationPartCopyWith<$Res> implements $ConversationPartCopyWith<$Res> {
  factory _$ConversationPartCopyWith(_ConversationPart value, $Res Function(_ConversationPart) _then) = __$ConversationPartCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "order") int? order,@JsonKey(name: "title") String? title,@JsonKey(name: "video_url") String? video,@JsonKey(name: "video_controller", fromJson: videoControllerFromJson) dynamic videoController,@JsonKey(name: "video_meta") VideoMeta? videoMeta,@JsonKey(name: "img_url") String? image, bool isBookmarked
});


@override $VideoMetaCopyWith<$Res>? get videoMeta;

}
/// @nodoc
class __$ConversationPartCopyWithImpl<$Res>
    implements _$ConversationPartCopyWith<$Res> {
  __$ConversationPartCopyWithImpl(this._self, this._then);

  final _ConversationPart _self;
  final $Res Function(_ConversationPart) _then;

/// Create a copy of ConversationPart
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? order = freezed,Object? title = freezed,Object? video = freezed,Object? videoController = freezed,Object? videoMeta = freezed,Object? image = freezed,Object? isBookmarked = null,}) {
  return _then(_ConversationPart(
order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,video: freezed == video ? _self.video : video // ignore: cast_nullable_to_non_nullable
as String?,videoController: freezed == videoController ? _self.videoController : videoController // ignore: cast_nullable_to_non_nullable
as dynamic,videoMeta: freezed == videoMeta ? _self.videoMeta : videoMeta // ignore: cast_nullable_to_non_nullable
as VideoMeta?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of ConversationPart
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VideoMetaCopyWith<$Res>? get videoMeta {
    if (_self.videoMeta == null) {
    return null;
  }

  return $VideoMetaCopyWith<$Res>(_self.videoMeta!, (value) {
    return _then(_self.copyWith(videoMeta: value));
  });
}
}


/// @nodoc
mixin _$VideoMeta {

@JsonKey(name: "duration", fromJson: durationFromJson) Duration? get duration; bool get isPlayed;
/// Create a copy of VideoMeta
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VideoMetaCopyWith<VideoMeta> get copyWith => _$VideoMetaCopyWithImpl<VideoMeta>(this as VideoMeta, _$identity);

  /// Serializes this VideoMeta to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VideoMeta&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.isPlayed, isPlayed) || other.isPlayed == isPlayed));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,duration,isPlayed);

@override
String toString() {
  return 'VideoMeta(duration: $duration, isPlayed: $isPlayed)';
}


}

/// @nodoc
abstract mixin class $VideoMetaCopyWith<$Res>  {
  factory $VideoMetaCopyWith(VideoMeta value, $Res Function(VideoMeta) _then) = _$VideoMetaCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "duration", fromJson: durationFromJson) Duration? duration, bool isPlayed
});




}
/// @nodoc
class _$VideoMetaCopyWithImpl<$Res>
    implements $VideoMetaCopyWith<$Res> {
  _$VideoMetaCopyWithImpl(this._self, this._then);

  final VideoMeta _self;
  final $Res Function(VideoMeta) _then;

/// Create a copy of VideoMeta
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? duration = freezed,Object? isPlayed = null,}) {
  return _then(_self.copyWith(
duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as Duration?,isPlayed: null == isPlayed ? _self.isPlayed : isPlayed // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VideoMeta].
extension VideoMetaPatterns on VideoMeta {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VideoMeta value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VideoMeta() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VideoMeta value)  $default,){
final _that = this;
switch (_that) {
case _VideoMeta():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VideoMeta value)?  $default,){
final _that = this;
switch (_that) {
case _VideoMeta() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "duration", fromJson: durationFromJson)  Duration? duration,  bool isPlayed)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VideoMeta() when $default != null:
return $default(_that.duration,_that.isPlayed);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "duration", fromJson: durationFromJson)  Duration? duration,  bool isPlayed)  $default,) {final _that = this;
switch (_that) {
case _VideoMeta():
return $default(_that.duration,_that.isPlayed);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "duration", fromJson: durationFromJson)  Duration? duration,  bool isPlayed)?  $default,) {final _that = this;
switch (_that) {
case _VideoMeta() when $default != null:
return $default(_that.duration,_that.isPlayed);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VideoMeta implements VideoMeta {
  const _VideoMeta({@JsonKey(name: "duration", fromJson: durationFromJson) this.duration, this.isPlayed = false});
  factory _VideoMeta.fromJson(Map<String, dynamic> json) => _$VideoMetaFromJson(json);

@override@JsonKey(name: "duration", fromJson: durationFromJson) final  Duration? duration;
@override@JsonKey() final  bool isPlayed;

/// Create a copy of VideoMeta
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VideoMetaCopyWith<_VideoMeta> get copyWith => __$VideoMetaCopyWithImpl<_VideoMeta>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VideoMetaToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VideoMeta&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.isPlayed, isPlayed) || other.isPlayed == isPlayed));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,duration,isPlayed);

@override
String toString() {
  return 'VideoMeta(duration: $duration, isPlayed: $isPlayed)';
}


}

/// @nodoc
abstract mixin class _$VideoMetaCopyWith<$Res> implements $VideoMetaCopyWith<$Res> {
  factory _$VideoMetaCopyWith(_VideoMeta value, $Res Function(_VideoMeta) _then) = __$VideoMetaCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "duration", fromJson: durationFromJson) Duration? duration, bool isPlayed
});




}
/// @nodoc
class __$VideoMetaCopyWithImpl<$Res>
    implements _$VideoMetaCopyWith<$Res> {
  __$VideoMetaCopyWithImpl(this._self, this._then);

  final _VideoMeta _self;
  final $Res Function(_VideoMeta) _then;

/// Create a copy of VideoMeta
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? duration = freezed,Object? isPlayed = null,}) {
  return _then(_VideoMeta(
duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as Duration?,isPlayed: null == isPlayed ? _self.isPlayed : isPlayed // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$AudioPath {

 String get path; String get refPath; String get url;
/// Create a copy of AudioPath
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AudioPathCopyWith<AudioPath> get copyWith => _$AudioPathCopyWithImpl<AudioPath>(this as AudioPath, _$identity);

  /// Serializes this AudioPath to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AudioPath&&(identical(other.path, path) || other.path == path)&&(identical(other.refPath, refPath) || other.refPath == refPath)&&(identical(other.url, url) || other.url == url));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,path,refPath,url);

@override
String toString() {
  return 'AudioPath(path: $path, refPath: $refPath, url: $url)';
}


}

/// @nodoc
abstract mixin class $AudioPathCopyWith<$Res>  {
  factory $AudioPathCopyWith(AudioPath value, $Res Function(AudioPath) _then) = _$AudioPathCopyWithImpl;
@useResult
$Res call({
 String path, String refPath, String url
});




}
/// @nodoc
class _$AudioPathCopyWithImpl<$Res>
    implements $AudioPathCopyWith<$Res> {
  _$AudioPathCopyWithImpl(this._self, this._then);

  final AudioPath _self;
  final $Res Function(AudioPath) _then;

/// Create a copy of AudioPath
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? path = null,Object? refPath = null,Object? url = null,}) {
  return _then(_self.copyWith(
path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,refPath: null == refPath ? _self.refPath : refPath // ignore: cast_nullable_to_non_nullable
as String,url: null == url ? _self.url : url // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [AudioPath].
extension AudioPathPatterns on AudioPath {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AudioPath value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AudioPath() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AudioPath value)  $default,){
final _that = this;
switch (_that) {
case _AudioPath():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AudioPath value)?  $default,){
final _that = this;
switch (_that) {
case _AudioPath() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String path,  String refPath,  String url)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AudioPath() when $default != null:
return $default(_that.path,_that.refPath,_that.url);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String path,  String refPath,  String url)  $default,) {final _that = this;
switch (_that) {
case _AudioPath():
return $default(_that.path,_that.refPath,_that.url);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String path,  String refPath,  String url)?  $default,) {final _that = this;
switch (_that) {
case _AudioPath() when $default != null:
return $default(_that.path,_that.refPath,_that.url);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AudioPath implements AudioPath {
  const _AudioPath({this.path = '', this.refPath = '', this.url = ''});
  factory _AudioPath.fromJson(Map<String, dynamic> json) => _$AudioPathFromJson(json);

@override@JsonKey() final  String path;
@override@JsonKey() final  String refPath;
@override@JsonKey() final  String url;

/// Create a copy of AudioPath
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AudioPathCopyWith<_AudioPath> get copyWith => __$AudioPathCopyWithImpl<_AudioPath>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AudioPathToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AudioPath&&(identical(other.path, path) || other.path == path)&&(identical(other.refPath, refPath) || other.refPath == refPath)&&(identical(other.url, url) || other.url == url));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,path,refPath,url);

@override
String toString() {
  return 'AudioPath(path: $path, refPath: $refPath, url: $url)';
}


}

/// @nodoc
abstract mixin class _$AudioPathCopyWith<$Res> implements $AudioPathCopyWith<$Res> {
  factory _$AudioPathCopyWith(_AudioPath value, $Res Function(_AudioPath) _then) = __$AudioPathCopyWithImpl;
@override @useResult
$Res call({
 String path, String refPath, String url
});




}
/// @nodoc
class __$AudioPathCopyWithImpl<$Res>
    implements _$AudioPathCopyWith<$Res> {
  __$AudioPathCopyWithImpl(this._self, this._then);

  final _AudioPath _self;
  final $Res Function(_AudioPath) _then;

/// Create a copy of AudioPath
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? path = null,Object? refPath = null,Object? url = null,}) {
  return _then(_AudioPath(
path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,refPath: null == refPath ? _self.refPath : refPath // ignore: cast_nullable_to_non_nullable
as String,url: null == url ? _self.url : url // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$ListeningPart {

@JsonKey(name: "audio_url") String? get main;@JsonKey(name: "order") int? get order;@JsonKey(name: "title") String? get title;@JsonKey(name: "image_url") String? get image; bool get isBookmarked;@JsonKey(name: "questions") List<Question> get questions;
/// Create a copy of ListeningPart
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ListeningPartCopyWith<ListeningPart> get copyWith => _$ListeningPartCopyWithImpl<ListeningPart>(this as ListeningPart, _$identity);

  /// Serializes this ListeningPart to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ListeningPart&&(identical(other.main, main) || other.main == main)&&(identical(other.order, order) || other.order == order)&&(identical(other.title, title) || other.title == title)&&(identical(other.image, image) || other.image == image)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked)&&const DeepCollectionEquality().equals(other.questions, questions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,main,order,title,image,isBookmarked,const DeepCollectionEquality().hash(questions));

@override
String toString() {
  return 'ListeningPart(main: $main, order: $order, title: $title, image: $image, isBookmarked: $isBookmarked, questions: $questions)';
}


}

/// @nodoc
abstract mixin class $ListeningPartCopyWith<$Res>  {
  factory $ListeningPartCopyWith(ListeningPart value, $Res Function(ListeningPart) _then) = _$ListeningPartCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "audio_url") String? main,@JsonKey(name: "order") int? order,@JsonKey(name: "title") String? title,@JsonKey(name: "image_url") String? image, bool isBookmarked,@JsonKey(name: "questions") List<Question> questions
});




}
/// @nodoc
class _$ListeningPartCopyWithImpl<$Res>
    implements $ListeningPartCopyWith<$Res> {
  _$ListeningPartCopyWithImpl(this._self, this._then);

  final ListeningPart _self;
  final $Res Function(ListeningPart) _then;

/// Create a copy of ListeningPart
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? main = freezed,Object? order = freezed,Object? title = freezed,Object? image = freezed,Object? isBookmarked = null,Object? questions = null,}) {
  return _then(_self.copyWith(
main: freezed == main ? _self.main : main // ignore: cast_nullable_to_non_nullable
as String?,order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,questions: null == questions ? _self.questions : questions // ignore: cast_nullable_to_non_nullable
as List<Question>,
  ));
}

}


/// Adds pattern-matching-related methods to [ListeningPart].
extension ListeningPartPatterns on ListeningPart {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ListeningPart value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ListeningPart() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ListeningPart value)  $default,){
final _that = this;
switch (_that) {
case _ListeningPart():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ListeningPart value)?  $default,){
final _that = this;
switch (_that) {
case _ListeningPart() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "audio_url")  String? main, @JsonKey(name: "order")  int? order, @JsonKey(name: "title")  String? title, @JsonKey(name: "image_url")  String? image,  bool isBookmarked, @JsonKey(name: "questions")  List<Question> questions)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ListeningPart() when $default != null:
return $default(_that.main,_that.order,_that.title,_that.image,_that.isBookmarked,_that.questions);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "audio_url")  String? main, @JsonKey(name: "order")  int? order, @JsonKey(name: "title")  String? title, @JsonKey(name: "image_url")  String? image,  bool isBookmarked, @JsonKey(name: "questions")  List<Question> questions)  $default,) {final _that = this;
switch (_that) {
case _ListeningPart():
return $default(_that.main,_that.order,_that.title,_that.image,_that.isBookmarked,_that.questions);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "audio_url")  String? main, @JsonKey(name: "order")  int? order, @JsonKey(name: "title")  String? title, @JsonKey(name: "image_url")  String? image,  bool isBookmarked, @JsonKey(name: "questions")  List<Question> questions)?  $default,) {final _that = this;
switch (_that) {
case _ListeningPart() when $default != null:
return $default(_that.main,_that.order,_that.title,_that.image,_that.isBookmarked,_that.questions);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ListeningPart implements ListeningPart {
  const _ListeningPart({@JsonKey(name: "audio_url") this.main, @JsonKey(name: "order") this.order, @JsonKey(name: "title") this.title, @JsonKey(name: "image_url") this.image, this.isBookmarked = false, @JsonKey(name: "questions") final  List<Question> questions = const []}): _questions = questions;
  factory _ListeningPart.fromJson(Map<String, dynamic> json) => _$ListeningPartFromJson(json);

@override@JsonKey(name: "audio_url") final  String? main;
@override@JsonKey(name: "order") final  int? order;
@override@JsonKey(name: "title") final  String? title;
@override@JsonKey(name: "image_url") final  String? image;
@override@JsonKey() final  bool isBookmarked;
 final  List<Question> _questions;
@override@JsonKey(name: "questions") List<Question> get questions {
  if (_questions is EqualUnmodifiableListView) return _questions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_questions);
}


/// Create a copy of ListeningPart
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ListeningPartCopyWith<_ListeningPart> get copyWith => __$ListeningPartCopyWithImpl<_ListeningPart>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ListeningPartToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ListeningPart&&(identical(other.main, main) || other.main == main)&&(identical(other.order, order) || other.order == order)&&(identical(other.title, title) || other.title == title)&&(identical(other.image, image) || other.image == image)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked)&&const DeepCollectionEquality().equals(other._questions, _questions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,main,order,title,image,isBookmarked,const DeepCollectionEquality().hash(_questions));

@override
String toString() {
  return 'ListeningPart(main: $main, order: $order, title: $title, image: $image, isBookmarked: $isBookmarked, questions: $questions)';
}


}

/// @nodoc
abstract mixin class _$ListeningPartCopyWith<$Res> implements $ListeningPartCopyWith<$Res> {
  factory _$ListeningPartCopyWith(_ListeningPart value, $Res Function(_ListeningPart) _then) = __$ListeningPartCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "audio_url") String? main,@JsonKey(name: "order") int? order,@JsonKey(name: "title") String? title,@JsonKey(name: "image_url") String? image, bool isBookmarked,@JsonKey(name: "questions") List<Question> questions
});




}
/// @nodoc
class __$ListeningPartCopyWithImpl<$Res>
    implements _$ListeningPartCopyWith<$Res> {
  __$ListeningPartCopyWithImpl(this._self, this._then);

  final _ListeningPart _self;
  final $Res Function(_ListeningPart) _then;

/// Create a copy of ListeningPart
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? main = freezed,Object? order = freezed,Object? title = freezed,Object? image = freezed,Object? isBookmarked = null,Object? questions = null,}) {
  return _then(_ListeningPart(
main: freezed == main ? _self.main : main // ignore: cast_nullable_to_non_nullable
as String?,order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,title: freezed == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String?,image: freezed == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String?,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,questions: null == questions ? _self._questions : questions // ignore: cast_nullable_to_non_nullable
as List<Question>,
  ));
}


}


/// @nodoc
mixin _$Question {

@JsonKey(name: "collapse") bool? get collapse;@JsonKey(name: "order") int? get order;@JsonKey(name: "question") String? get question; String? get answer; bool? get isCorrect;@JsonKey(name: "choices") List<Choice> get choices;
/// Create a copy of Question
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$QuestionCopyWith<Question> get copyWith => _$QuestionCopyWithImpl<Question>(this as Question, _$identity);

  /// Serializes this Question to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Question&&(identical(other.collapse, collapse) || other.collapse == collapse)&&(identical(other.order, order) || other.order == order)&&(identical(other.question, question) || other.question == question)&&(identical(other.answer, answer) || other.answer == answer)&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect)&&const DeepCollectionEquality().equals(other.choices, choices));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,collapse,order,question,answer,isCorrect,const DeepCollectionEquality().hash(choices));

@override
String toString() {
  return 'Question(collapse: $collapse, order: $order, question: $question, answer: $answer, isCorrect: $isCorrect, choices: $choices)';
}


}

/// @nodoc
abstract mixin class $QuestionCopyWith<$Res>  {
  factory $QuestionCopyWith(Question value, $Res Function(Question) _then) = _$QuestionCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "collapse") bool? collapse,@JsonKey(name: "order") int? order,@JsonKey(name: "question") String? question, String? answer, bool? isCorrect,@JsonKey(name: "choices") List<Choice> choices
});




}
/// @nodoc
class _$QuestionCopyWithImpl<$Res>
    implements $QuestionCopyWith<$Res> {
  _$QuestionCopyWithImpl(this._self, this._then);

  final Question _self;
  final $Res Function(Question) _then;

/// Create a copy of Question
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? collapse = freezed,Object? order = freezed,Object? question = freezed,Object? answer = freezed,Object? isCorrect = freezed,Object? choices = null,}) {
  return _then(_self.copyWith(
collapse: freezed == collapse ? _self.collapse : collapse // ignore: cast_nullable_to_non_nullable
as bool?,order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,question: freezed == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as String?,answer: freezed == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as String?,isCorrect: freezed == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool?,choices: null == choices ? _self.choices : choices // ignore: cast_nullable_to_non_nullable
as List<Choice>,
  ));
}

}


/// Adds pattern-matching-related methods to [Question].
extension QuestionPatterns on Question {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Question value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Question() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Question value)  $default,){
final _that = this;
switch (_that) {
case _Question():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Question value)?  $default,){
final _that = this;
switch (_that) {
case _Question() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "collapse")  bool? collapse, @JsonKey(name: "order")  int? order, @JsonKey(name: "question")  String? question,  String? answer,  bool? isCorrect, @JsonKey(name: "choices")  List<Choice> choices)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Question() when $default != null:
return $default(_that.collapse,_that.order,_that.question,_that.answer,_that.isCorrect,_that.choices);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "collapse")  bool? collapse, @JsonKey(name: "order")  int? order, @JsonKey(name: "question")  String? question,  String? answer,  bool? isCorrect, @JsonKey(name: "choices")  List<Choice> choices)  $default,) {final _that = this;
switch (_that) {
case _Question():
return $default(_that.collapse,_that.order,_that.question,_that.answer,_that.isCorrect,_that.choices);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "collapse")  bool? collapse, @JsonKey(name: "order")  int? order, @JsonKey(name: "question")  String? question,  String? answer,  bool? isCorrect, @JsonKey(name: "choices")  List<Choice> choices)?  $default,) {final _that = this;
switch (_that) {
case _Question() when $default != null:
return $default(_that.collapse,_that.order,_that.question,_that.answer,_that.isCorrect,_that.choices);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Question implements Question {
  const _Question({@JsonKey(name: "collapse") this.collapse, @JsonKey(name: "order") this.order, @JsonKey(name: "question") this.question, this.answer, this.isCorrect, @JsonKey(name: "choices") final  List<Choice> choices = const []}): _choices = choices;
  factory _Question.fromJson(Map<String, dynamic> json) => _$QuestionFromJson(json);

@override@JsonKey(name: "collapse") final  bool? collapse;
@override@JsonKey(name: "order") final  int? order;
@override@JsonKey(name: "question") final  String? question;
@override final  String? answer;
@override final  bool? isCorrect;
 final  List<Choice> _choices;
@override@JsonKey(name: "choices") List<Choice> get choices {
  if (_choices is EqualUnmodifiableListView) return _choices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_choices);
}


/// Create a copy of Question
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$QuestionCopyWith<_Question> get copyWith => __$QuestionCopyWithImpl<_Question>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$QuestionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Question&&(identical(other.collapse, collapse) || other.collapse == collapse)&&(identical(other.order, order) || other.order == order)&&(identical(other.question, question) || other.question == question)&&(identical(other.answer, answer) || other.answer == answer)&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect)&&const DeepCollectionEquality().equals(other._choices, _choices));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,collapse,order,question,answer,isCorrect,const DeepCollectionEquality().hash(_choices));

@override
String toString() {
  return 'Question(collapse: $collapse, order: $order, question: $question, answer: $answer, isCorrect: $isCorrect, choices: $choices)';
}


}

/// @nodoc
abstract mixin class _$QuestionCopyWith<$Res> implements $QuestionCopyWith<$Res> {
  factory _$QuestionCopyWith(_Question value, $Res Function(_Question) _then) = __$QuestionCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "collapse") bool? collapse,@JsonKey(name: "order") int? order,@JsonKey(name: "question") String? question, String? answer, bool? isCorrect,@JsonKey(name: "choices") List<Choice> choices
});




}
/// @nodoc
class __$QuestionCopyWithImpl<$Res>
    implements _$QuestionCopyWith<$Res> {
  __$QuestionCopyWithImpl(this._self, this._then);

  final _Question _self;
  final $Res Function(_Question) _then;

/// Create a copy of Question
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? collapse = freezed,Object? order = freezed,Object? question = freezed,Object? answer = freezed,Object? isCorrect = freezed,Object? choices = null,}) {
  return _then(_Question(
collapse: freezed == collapse ? _self.collapse : collapse // ignore: cast_nullable_to_non_nullable
as bool?,order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int?,question: freezed == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as String?,answer: freezed == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as String?,isCorrect: freezed == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool?,choices: null == choices ? _self._choices : choices // ignore: cast_nullable_to_non_nullable
as List<Choice>,
  ));
}


}


/// @nodoc
mixin _$Choice {

@JsonKey(name: "is_correct") bool? get isCorrect;@JsonKey(name: "text") String? get text;
/// Create a copy of Choice
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChoiceCopyWith<Choice> get copyWith => _$ChoiceCopyWithImpl<Choice>(this as Choice, _$identity);

  /// Serializes this Choice to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Choice&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect)&&(identical(other.text, text) || other.text == text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isCorrect,text);

@override
String toString() {
  return 'Choice(isCorrect: $isCorrect, text: $text)';
}


}

/// @nodoc
abstract mixin class $ChoiceCopyWith<$Res>  {
  factory $ChoiceCopyWith(Choice value, $Res Function(Choice) _then) = _$ChoiceCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "is_correct") bool? isCorrect,@JsonKey(name: "text") String? text
});




}
/// @nodoc
class _$ChoiceCopyWithImpl<$Res>
    implements $ChoiceCopyWith<$Res> {
  _$ChoiceCopyWithImpl(this._self, this._then);

  final Choice _self;
  final $Res Function(Choice) _then;

/// Create a copy of Choice
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isCorrect = freezed,Object? text = freezed,}) {
  return _then(_self.copyWith(
isCorrect: freezed == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool?,text: freezed == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [Choice].
extension ChoicePatterns on Choice {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Choice value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Choice() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Choice value)  $default,){
final _that = this;
switch (_that) {
case _Choice():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Choice value)?  $default,){
final _that = this;
switch (_that) {
case _Choice() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "is_correct")  bool? isCorrect, @JsonKey(name: "text")  String? text)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Choice() when $default != null:
return $default(_that.isCorrect,_that.text);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "is_correct")  bool? isCorrect, @JsonKey(name: "text")  String? text)  $default,) {final _that = this;
switch (_that) {
case _Choice():
return $default(_that.isCorrect,_that.text);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "is_correct")  bool? isCorrect, @JsonKey(name: "text")  String? text)?  $default,) {final _that = this;
switch (_that) {
case _Choice() when $default != null:
return $default(_that.isCorrect,_that.text);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Choice implements Choice {
  const _Choice({@JsonKey(name: "is_correct") this.isCorrect, @JsonKey(name: "text") this.text});
  factory _Choice.fromJson(Map<String, dynamic> json) => _$ChoiceFromJson(json);

@override@JsonKey(name: "is_correct") final  bool? isCorrect;
@override@JsonKey(name: "text") final  String? text;

/// Create a copy of Choice
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChoiceCopyWith<_Choice> get copyWith => __$ChoiceCopyWithImpl<_Choice>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChoiceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Choice&&(identical(other.isCorrect, isCorrect) || other.isCorrect == isCorrect)&&(identical(other.text, text) || other.text == text));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isCorrect,text);

@override
String toString() {
  return 'Choice(isCorrect: $isCorrect, text: $text)';
}


}

/// @nodoc
abstract mixin class _$ChoiceCopyWith<$Res> implements $ChoiceCopyWith<$Res> {
  factory _$ChoiceCopyWith(_Choice value, $Res Function(_Choice) _then) = __$ChoiceCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "is_correct") bool? isCorrect,@JsonKey(name: "text") String? text
});




}
/// @nodoc
class __$ChoiceCopyWithImpl<$Res>
    implements _$ChoiceCopyWith<$Res> {
  __$ChoiceCopyWithImpl(this._self, this._then);

  final _Choice _self;
  final $Res Function(_Choice) _then;

/// Create a copy of Choice
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isCorrect = freezed,Object? text = freezed,}) {
  return _then(_Choice(
isCorrect: freezed == isCorrect ? _self.isCorrect : isCorrect // ignore: cast_nullable_to_non_nullable
as bool?,text: freezed == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$SpeakingPart {

@JsonKey(name: "answer") Answer get answer;@JsonKey(name: "question") Answer get question;@JsonKey(name: "image_url") String get image;@JsonKey(name: "imageB_url") String get imageB;@JsonKey(name: "order") int get order;@JsonKey(name: "title") String get title; bool get isBookmarked;
/// Create a copy of SpeakingPart
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SpeakingPartCopyWith<SpeakingPart> get copyWith => _$SpeakingPartCopyWithImpl<SpeakingPart>(this as SpeakingPart, _$identity);

  /// Serializes this SpeakingPart to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SpeakingPart&&(identical(other.answer, answer) || other.answer == answer)&&(identical(other.question, question) || other.question == question)&&(identical(other.image, image) || other.image == image)&&(identical(other.imageB, imageB) || other.imageB == imageB)&&(identical(other.order, order) || other.order == order)&&(identical(other.title, title) || other.title == title)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,answer,question,image,imageB,order,title,isBookmarked);

@override
String toString() {
  return 'SpeakingPart(answer: $answer, question: $question, image: $image, imageB: $imageB, order: $order, title: $title, isBookmarked: $isBookmarked)';
}


}

/// @nodoc
abstract mixin class $SpeakingPartCopyWith<$Res>  {
  factory $SpeakingPartCopyWith(SpeakingPart value, $Res Function(SpeakingPart) _then) = _$SpeakingPartCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "answer") Answer answer,@JsonKey(name: "question") Answer question,@JsonKey(name: "image_url") String image,@JsonKey(name: "imageB_url") String imageB,@JsonKey(name: "order") int order,@JsonKey(name: "title") String title, bool isBookmarked
});


$AnswerCopyWith<$Res> get answer;$AnswerCopyWith<$Res> get question;

}
/// @nodoc
class _$SpeakingPartCopyWithImpl<$Res>
    implements $SpeakingPartCopyWith<$Res> {
  _$SpeakingPartCopyWithImpl(this._self, this._then);

  final SpeakingPart _self;
  final $Res Function(SpeakingPart) _then;

/// Create a copy of SpeakingPart
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? answer = null,Object? question = null,Object? image = null,Object? imageB = null,Object? order = null,Object? title = null,Object? isBookmarked = null,}) {
  return _then(_self.copyWith(
answer: null == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as Answer,question: null == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as Answer,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,imageB: null == imageB ? _self.imageB : imageB // ignore: cast_nullable_to_non_nullable
as String,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of SpeakingPart
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AnswerCopyWith<$Res> get answer {
  
  return $AnswerCopyWith<$Res>(_self.answer, (value) {
    return _then(_self.copyWith(answer: value));
  });
}/// Create a copy of SpeakingPart
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AnswerCopyWith<$Res> get question {
  
  return $AnswerCopyWith<$Res>(_self.question, (value) {
    return _then(_self.copyWith(question: value));
  });
}
}


/// Adds pattern-matching-related methods to [SpeakingPart].
extension SpeakingPartPatterns on SpeakingPart {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SpeakingPart value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SpeakingPart() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SpeakingPart value)  $default,){
final _that = this;
switch (_that) {
case _SpeakingPart():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SpeakingPart value)?  $default,){
final _that = this;
switch (_that) {
case _SpeakingPart() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "answer")  Answer answer, @JsonKey(name: "question")  Answer question, @JsonKey(name: "image_url")  String image, @JsonKey(name: "imageB_url")  String imageB, @JsonKey(name: "order")  int order, @JsonKey(name: "title")  String title,  bool isBookmarked)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SpeakingPart() when $default != null:
return $default(_that.answer,_that.question,_that.image,_that.imageB,_that.order,_that.title,_that.isBookmarked);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "answer")  Answer answer, @JsonKey(name: "question")  Answer question, @JsonKey(name: "image_url")  String image, @JsonKey(name: "imageB_url")  String imageB, @JsonKey(name: "order")  int order, @JsonKey(name: "title")  String title,  bool isBookmarked)  $default,) {final _that = this;
switch (_that) {
case _SpeakingPart():
return $default(_that.answer,_that.question,_that.image,_that.imageB,_that.order,_that.title,_that.isBookmarked);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "answer")  Answer answer, @JsonKey(name: "question")  Answer question, @JsonKey(name: "image_url")  String image, @JsonKey(name: "imageB_url")  String imageB, @JsonKey(name: "order")  int order, @JsonKey(name: "title")  String title,  bool isBookmarked)?  $default,) {final _that = this;
switch (_that) {
case _SpeakingPart() when $default != null:
return $default(_that.answer,_that.question,_that.image,_that.imageB,_that.order,_that.title,_that.isBookmarked);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SpeakingPart implements SpeakingPart {
  const _SpeakingPart({@JsonKey(name: "answer") required this.answer, @JsonKey(name: "question") required this.question, @JsonKey(name: "image_url") required this.image, @JsonKey(name: "imageB_url") required this.imageB, @JsonKey(name: "order") required this.order, @JsonKey(name: "title") required this.title, this.isBookmarked = false});
  factory _SpeakingPart.fromJson(Map<String, dynamic> json) => _$SpeakingPartFromJson(json);

@override@JsonKey(name: "answer") final  Answer answer;
@override@JsonKey(name: "question") final  Answer question;
@override@JsonKey(name: "image_url") final  String image;
@override@JsonKey(name: "imageB_url") final  String imageB;
@override@JsonKey(name: "order") final  int order;
@override@JsonKey(name: "title") final  String title;
@override@JsonKey() final  bool isBookmarked;

/// Create a copy of SpeakingPart
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SpeakingPartCopyWith<_SpeakingPart> get copyWith => __$SpeakingPartCopyWithImpl<_SpeakingPart>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SpeakingPartToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SpeakingPart&&(identical(other.answer, answer) || other.answer == answer)&&(identical(other.question, question) || other.question == question)&&(identical(other.image, image) || other.image == image)&&(identical(other.imageB, imageB) || other.imageB == imageB)&&(identical(other.order, order) || other.order == order)&&(identical(other.title, title) || other.title == title)&&(identical(other.isBookmarked, isBookmarked) || other.isBookmarked == isBookmarked));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,answer,question,image,imageB,order,title,isBookmarked);

@override
String toString() {
  return 'SpeakingPart(answer: $answer, question: $question, image: $image, imageB: $imageB, order: $order, title: $title, isBookmarked: $isBookmarked)';
}


}

/// @nodoc
abstract mixin class _$SpeakingPartCopyWith<$Res> implements $SpeakingPartCopyWith<$Res> {
  factory _$SpeakingPartCopyWith(_SpeakingPart value, $Res Function(_SpeakingPart) _then) = __$SpeakingPartCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "answer") Answer answer,@JsonKey(name: "question") Answer question,@JsonKey(name: "image_url") String image,@JsonKey(name: "imageB_url") String imageB,@JsonKey(name: "order") int order,@JsonKey(name: "title") String title, bool isBookmarked
});


@override $AnswerCopyWith<$Res> get answer;@override $AnswerCopyWith<$Res> get question;

}
/// @nodoc
class __$SpeakingPartCopyWithImpl<$Res>
    implements _$SpeakingPartCopyWith<$Res> {
  __$SpeakingPartCopyWithImpl(this._self, this._then);

  final _SpeakingPart _self;
  final $Res Function(_SpeakingPart) _then;

/// Create a copy of SpeakingPart
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? answer = null,Object? question = null,Object? image = null,Object? imageB = null,Object? order = null,Object? title = null,Object? isBookmarked = null,}) {
  return _then(_SpeakingPart(
answer: null == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as Answer,question: null == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as Answer,image: null == image ? _self.image : image // ignore: cast_nullable_to_non_nullable
as String,imageB: null == imageB ? _self.imageB : imageB // ignore: cast_nullable_to_non_nullable
as String,order: null == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as int,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,isBookmarked: null == isBookmarked ? _self.isBookmarked : isBookmarked // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of SpeakingPart
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AnswerCopyWith<$Res> get answer {
  
  return $AnswerCopyWith<$Res>(_self.answer, (value) {
    return _then(_self.copyWith(answer: value));
  });
}/// Create a copy of SpeakingPart
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AnswerCopyWith<$Res> get question {
  
  return $AnswerCopyWith<$Res>(_self.question, (value) {
    return _then(_self.copyWith(question: value));
  });
}
}


/// @nodoc
mixin _$Answer {

@JsonKey(name: "audio_url") String get audio;@JsonKey(name: "text") String get text; bool get isActive;
/// Create a copy of Answer
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AnswerCopyWith<Answer> get copyWith => _$AnswerCopyWithImpl<Answer>(this as Answer, _$identity);

  /// Serializes this Answer to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Answer&&(identical(other.audio, audio) || other.audio == audio)&&(identical(other.text, text) || other.text == text)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,audio,text,isActive);

@override
String toString() {
  return 'Answer(audio: $audio, text: $text, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class $AnswerCopyWith<$Res>  {
  factory $AnswerCopyWith(Answer value, $Res Function(Answer) _then) = _$AnswerCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: "audio_url") String audio,@JsonKey(name: "text") String text, bool isActive
});




}
/// @nodoc
class _$AnswerCopyWithImpl<$Res>
    implements $AnswerCopyWith<$Res> {
  _$AnswerCopyWithImpl(this._self, this._then);

  final Answer _self;
  final $Res Function(Answer) _then;

/// Create a copy of Answer
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? audio = null,Object? text = null,Object? isActive = null,}) {
  return _then(_self.copyWith(
audio: null == audio ? _self.audio : audio // ignore: cast_nullable_to_non_nullable
as String,text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [Answer].
extension AnswerPatterns on Answer {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Answer value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Answer() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Answer value)  $default,){
final _that = this;
switch (_that) {
case _Answer():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Answer value)?  $default,){
final _that = this;
switch (_that) {
case _Answer() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: "audio_url")  String audio, @JsonKey(name: "text")  String text,  bool isActive)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Answer() when $default != null:
return $default(_that.audio,_that.text,_that.isActive);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: "audio_url")  String audio, @JsonKey(name: "text")  String text,  bool isActive)  $default,) {final _that = this;
switch (_that) {
case _Answer():
return $default(_that.audio,_that.text,_that.isActive);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: "audio_url")  String audio, @JsonKey(name: "text")  String text,  bool isActive)?  $default,) {final _that = this;
switch (_that) {
case _Answer() when $default != null:
return $default(_that.audio,_that.text,_that.isActive);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Answer implements Answer {
  const _Answer({@JsonKey(name: "audio_url") required this.audio, @JsonKey(name: "text") required this.text, this.isActive = false});
  factory _Answer.fromJson(Map<String, dynamic> json) => _$AnswerFromJson(json);

@override@JsonKey(name: "audio_url") final  String audio;
@override@JsonKey(name: "text") final  String text;
@override@JsonKey() final  bool isActive;

/// Create a copy of Answer
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AnswerCopyWith<_Answer> get copyWith => __$AnswerCopyWithImpl<_Answer>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AnswerToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Answer&&(identical(other.audio, audio) || other.audio == audio)&&(identical(other.text, text) || other.text == text)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,audio,text,isActive);

@override
String toString() {
  return 'Answer(audio: $audio, text: $text, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class _$AnswerCopyWith<$Res> implements $AnswerCopyWith<$Res> {
  factory _$AnswerCopyWith(_Answer value, $Res Function(_Answer) _then) = __$AnswerCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: "audio_url") String audio,@JsonKey(name: "text") String text, bool isActive
});




}
/// @nodoc
class __$AnswerCopyWithImpl<$Res>
    implements _$AnswerCopyWith<$Res> {
  __$AnswerCopyWithImpl(this._self, this._then);

  final _Answer _self;
  final $Res Function(_Answer) _then;

/// Create a copy of Answer
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? audio = null,Object? text = null,Object? isActive = null,}) {
  return _then(_Answer(
audio: null == audio ? _self.audio : audio // ignore: cast_nullable_to_non_nullable
as String,text: null == text ? _self.text : text // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
