import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/main_lesson_state.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/pronunciation_state.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:collection/collection.dart';
part 'pronunciation_controller.g.dart';

final audioPlayerProvider = Provider.autoDispose<AudioPlayer>((ref) {
  return AudioPlayer();
});

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
@riverpod
class PronunciationController extends _$PronunciationController {
  late MainLessonRepository _mainLessonRepository;
  late UserDataServiceRepository _userDataServiceRepository;
  late ChapterContentStateNotifier _chapterContentStateNotifier;

  List<ContentIndexData> _paths = [];
  Set<String> _completedPaths = {};
  List<ContentIndexData> activePaths = [];
  int partOrder = 0;
  int? subPartOrder;
  int index = 0;
  int totalPaths = 0;
  int totalParts = 0;
  bool newSection = false;
  Map<int, int> totalSubParts = {};
  late MainLessonState _mainLessonState;

  @override
  FutureOr<PronunciationState> build(
    String level,
    String chapter,
    String path,
  ) async {
    _mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    _userDataServiceRepository = ref.watch(userDataServiceProvider);
    _chapterContentStateNotifier = ref.watch(
      chapterContentStateProvider.notifier,
    );

    late bool isIntro;

    await _mainLessonRepository.isIntro(lessonName: 'pronunciation').then((
      val,
    ) {
      val.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          isIntro = data;
        },
      );
    });

    _init(level, chapter, path);
    return PronunciationState(
      agregateScore: PronunciationAgregateScore(),
      isIntro: isIntro,
    );
  }

  Future<void> _init(String level, String chapter, String path) async {
    final result = await _mainLessonRepository.getPathIndex(
      level: level,
      chapter: chapter,
      section: SectionType.pronunciation,
    );
    await _mainLessonRepository.saveIntro(lessonName: 'pronunciation');
    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) async {
        _paths = data;

        final result = await _userDataServiceRepository.getPronunciationResult(
          PronunciationScoreParams(level: level, chapter: chapter),
        );

        result.fold(
          (failure) {
            state = AsyncError(failure.message, StackTrace.current);
          },
          (data) {
            _completedPaths = data.map((e) => e.path).toSet();
          },
        );

        _mainLessonState = ref.read(mainLessonStateProvider);

        state = AsyncData(state.value!.copyWith(paths: _paths));

        final groupByPart = groupBy(_paths, (item) => item.partOrder);
        totalParts = groupByPart.length;

        groupByPart.forEach((key, value) {
          totalSubParts[key!] =
              groupBy(value, (item) => item.subpartOrder).length;
        });

        if (path != 'blankpath') {
          final contentPath = utf8.decode(base64Url.decode(path));
          var contentData = _paths.firstWhere(
            (element) => element.contentPath == contentPath,
          );
          partOrder = contentData.partOrder ?? 0;
          subPartOrder = contentData.subpartOrder;
          _initPaths();
          index = activePaths.indexWhere(
            (element) => element.contentPath == contentPath,
          );
        } else if (_mainLessonState.lastPronunciation != null &&
            _mainLessonState.fromLastCourse == true) {
          partOrder = _mainLessonState.lastPronunciation!.partOrder ?? 0;
          subPartOrder = _mainLessonState.lastPronunciation!.subpartOrder;
          _initPaths();
          index = activePaths.indexWhere(
            (element) =>
                element.contentPath == _mainLessonState.lastPronunciation!.path,
          );
        } else {
          partOrder = 0;
          subPartOrder = 0;
          _initPaths();
          index = 0;
        }

        _initContent();
        saveLastCourse();
      },
    );
  }

  void _initPaths() {
    activePaths = _prepareActivePathList(partOrder, subPartOrder);
    totalPaths = activePaths.length;
  }

  List<ContentIndexData> _prepareActivePathList(
    int partOrder,
    int? subpartOrder,
  ) {
    if (totalSubParts[partOrder] == 1) {
      return _paths.where((element) => element.partOrder == partOrder).toList();
    } else {
      return _paths
          .where(
            (element) =>
                element.partOrder == partOrder &&
                element.subpartOrder == subpartOrder,
          )
          .toList();
    }
  }

  void _initContent() {
    state = AsyncData(state.value!.copyWith(response: null));
    final path = activePaths[index].contentPath;
    state = AsyncData(
      state.value!.copyWith(
        currentPath: activePaths[index],
        isNewSubpart: index == 0 ? true : false,
      ),
    );
    getContent(path: path);
  }

  Future<void> saveLastCourse() async {
    LastCourse data = LastCourse(
      accessTime: DateTime.now().toUtc(),
      level: level,
      chapter: int.parse(chapter),
      section: SectionType.pronunciation,
      partOrder: activePaths[index].partOrder,
      subpartOrder: activePaths[index].subpartOrder,
      path: activePaths[index].contentPath,
    );

    await _userDataServiceRepository.updateLastCourse(
      lastCourse: data,
      section: SectionType.pronunciation,
    );
    final mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateLastPronunciation(data);
    mainLessonStateNotifier.updateFromLastCourse(false);
  }

  Future<void> getContent({required String path}) async {
    final contentFuture = _mainLessonRepository.getPronunciation(path: path);
    final bookmarkFuture = _userDataServiceRepository.getBookmark(
      section: SectionType.pronunciation,
      path: path,
    );

    final results = await Future.wait([contentFuture, bookmarkFuture]);

    final contentResult =
        results[0] as Either<AppException, PronunciationSubPart>;
    final bookmarkResult = results[1] as Either<AppException, Bookmark>;

    if (contentResult.isLeft()) {
      final failure = contentResult.fold((l) => l, (r) => null);
      state = AsyncError(failure!.message, StackTrace.current);
      return;
    }
    if (bookmarkResult.isLeft()) {
      final failure = bookmarkResult.fold((l) => l, (r) => null);
      state = AsyncError(failure!.message, StackTrace.current);
      return;
    }

    PronunciationSubPart contentData =
        contentResult.fold((l) => null, (r) => r)!;
    final Bookmark bookmarkData = bookmarkResult.fold((l) => null, (r) => r)!;

    contentData = contentData.copyWith(
      isBookmarked: bookmarkData.path == '' ? false : bookmarkData.isBookmarked,
    );

    state = AsyncData(state.value!.copyWith(data: contentData));
  }

  Future<void> uploadAudio({
    required String path,
    required BuildContext context,
  }) async {
    state = AsyncData(state.value!.copyWith(audioPath: AudioPath(path: path)));
    checkPronunciation(context: context);
  }

  void changeRecordingState() {
    state = AsyncData(
      state.value!.copyWith(isRecording: !state.value!.isRecording),
    );
  }

  Future<void> checkPronunciation({required BuildContext context}) async {
    state = AsyncData(state.value!.copyWith(isLoading: true));
    final result = await _mainLessonRepository.checkPronunciation(
      audio: state.value!.audioPath!,
      text: state.value!.data!.caption,
      autoDelete: false,
    );
    result.fold(
      (failure) {
        state = AsyncData(state.value!.copyWith(isLoading: false));
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) async {
        state = AsyncData(
          state.value!.copyWith(response: data, isLoading: false),
        );

        await saveResult();

        if (context.mounted) {
          customNav(
            context,
            RouterName.pronunciationChallengeContentResult,
            isReplace: true,
            params: {'level': level, 'chapter': chapter, 'path': path},
          );
        }
      },
    );
  }

  Future<void> saveResult() async {
    if (state.value!.response != null) {
      final result = await _userDataServiceRepository.saveLessonResult(
        level: level,
        chapter: chapter,
        section: SectionType.pronunciation,
        result: LessonResult(
          partOrder: activePaths[index].partOrder,
          subpartOrder: activePaths[index].subpartOrder,
          contentOrder: activePaths[index].contentOrder,
          path: activePaths[index].contentPath,
          result: {
            'accuracyScore': state.value!.response!.accuracyScore,
            'fluencyScore': state.value!.response!.fluencyScore,
            'prosodyScore': state.value!.response!.prosodyScore,
            'completenessScore': state.value!.response!.completenessScore,
            'pronScore': state.value!.response!.pronScore,
          },
        ),
      );

      result.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.empty);
        },
        (data) {
          _completedPaths.add(activePaths[index].contentPath);
          _chapterContentStateNotifier.updatePronunciation(activePaths[index]);
          markSectionAsCompleted();
        },
      );
    }
  }

  Future<void> clearResponse() async {
    deleteLocalAudio();
    state = AsyncData(state.value!.copyWith(response: null));
  }

  Future<void> calculateAgregateResult() async {
    final partOrder = activePaths[index].partOrder;
    final subpartOrder = activePaths[index].subpartOrder;

    final result = await _userDataServiceRepository
        .calculatePronunciationResult(
          PronunciationScoreParams(
            level: level,
            chapter: chapter,
            partOrder: partOrder!,
            subpartOrder: subpartOrder,
          ),
        );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        state = AsyncData(state.value!.copyWith(agregateScore: data));
      },
    );
  }

  Future<void> deleteLocalAudio() async {
    if (state.value!.audioPath != null) {
      final filePath = state.value!.audioPath!.path;

      /**
       * Immediately set audioPath to null, dont wait for the file to be deleted
       */
      state = AsyncData(state.value!.copyWith(audioPath: null));

      // Delete the file, asychronously to improve performance
      () async {
        try {
          File file = File(filePath);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          print(e);
        }
      }();
    }
  }

  Future<void> nextQuestion() async {
    // End condition - if we've completed all parts
    if (partOrder >= totalParts) {
      state = AsyncData(state.value!.copyWith(nextSection: true));
      return;
    }

    if (index < activePaths.length - 1) {
      index++;
      await saveLastCourse();
      // _initPaths();
      _initContent();
      return;
    }

    // Reached end of current path
    index = 0;
    if (subPartOrder != null) {
      subPartOrder = subPartOrder! + 1;
      // Check if we've completed all subparts in current part
      if (subPartOrder! >= totalSubParts[partOrder]!) {
        partOrder++;
        subPartOrder = 0;

        // Check if we've completed all parts
        if (partOrder >= totalParts) {
          /**
         * Using state management, the nextSection value will only be updated
         * in the next tick. This caused error
         */
          newSection = true;
          state = AsyncData(state.value!.copyWith(nextSection: true));
          return;
        }
      }
    } else {
      partOrder++;

      // Check if we've completed all parts
      if (partOrder >= totalParts) {
        /**
         * Using state management, the nextSection value will only be updated
         * in the next tick. This caused error
         */
        newSection = true;
        state = AsyncData(state.value!.copyWith(nextSection: true));
        return;
      }
    }

    // Initialize for new subpart or part
    _initPaths();
    _initContent();
  }

  Future<void> markSectionAsCompleted() async {
    // Use set operations for O(n) performance instead of O(n*m)
    // Create a set of required paths and check if it's a subset of completed paths
    final requiredPaths =
        _paths.map((pathData) => pathData.contentPath).toSet();
    final allPathsCompleted = requiredPaths.difference(_completedPaths).isEmpty;

    if (allPathsCompleted) {
      await _userDataServiceRepository.setSectionCompleted(
        level: level,
        chapter: chapter,
        section: SectionType.pronunciation,
      );
    }
  }

  Future<void> showAgregateResult(BuildContext context) async {
    await calculateAgregateResult();
    markSectionAsCompleted();
    if (context.mounted) {
      customNav(
        context,
        RouterName.pronunciationChallengeResult,
        isReplace: true,
        params: {'level': level, 'chapter': chapter, 'path': path},
      );
    }
  }

  String prepareTitle() {
    String title = state.value!.currentPath?.partTitle ?? '';

    if (state.value!.currentPath?.subpartTitle != null) {
      title += ' - ${state.value!.currentPath?.subpartTitle}';
    }

    return title;
  }

  Future<void> saveBookmark() async {
    bool isBookmarked = !state.value!.data!.isBookmarked;

    final String partOrder =
        (activePaths[state.value!.currentPage].partOrder ?? 0).toString();
    final String subpartOrder =
        (activePaths[state.value!.currentPage].subpartOrder ?? 0).toString();
    final String contentOrder = (activePaths[index].contentOrder).toString();
    final String speakingStage = '';
    //(activePaths[index].speakingStage?.name ?? '');

    final String docId =
        '$level$chapter$partOrder$subpartOrder$contentOrder$speakingStage';
    final result = await _userDataServiceRepository.saveBookmark(
      section: SectionType.pronunciation,
      content: Bookmark(
        docId: docId,
        path: activePaths[index].contentPath,
        isBookmarked: isBookmarked,
      ),
    );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        state = AsyncData(
          state.value!.copyWith(
            data: state.value!.data!.copyWith(isBookmarked: isBookmarked),
          ),
        );
        activePaths[index] = activePaths[index].copyWith(
          isBookmarked: isBookmarked,
        );
        _chapterContentStateNotifier.updatePronunciation(activePaths[index]);
      },
    );
  }
}

/// Simple mock of a 401 exception
class UnauthorizedException implements Exception {
  const UnauthorizedException(this.message);
  final String message;
}

/// Mock of the duration of a network request
final networkRoundTripTime = 2.seconds;
