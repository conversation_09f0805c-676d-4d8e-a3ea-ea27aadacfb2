import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/listening_state.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/main_lesson_state.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';

part 'listening_controller.g.dart';

@riverpod
class ListeningController extends _$ListeningController {
  late MainLessonRepository mainLessonRepository;
  late UserDataServiceRepository _userDataServiceRepository;
  List<ContentIndexData> _paths = [];
  Set<String> _completedPaths = {};
  late MainLessonState _mainLessonState;
  late ChapterContentStateNotifier _chapterContentStateNotifier;

  @override
  FutureOr<ListeningState> build(
    String level,
    String chapter,
    String path,
  ) async {
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    _userDataServiceRepository = ref.watch(userDataServiceProvider);
    _chapterContentStateNotifier = ref.watch(
      chapterContentStateProvider.notifier,
    );
    late bool isIntroValue; // Renamed to avoid conflict

    await mainLessonRepository.isIntro(lessonName: 'listening').then((val) {
      val.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          isIntroValue = data;
        },
      );
    });
    // Initialize with a default state that includes isIntroValue
    final initialState = ListeningState(
      isIntro: isIntroValue,
      expandedQuestionIndex: null,
    );
    state = AsyncData(initialState); // Ensure state is AsyncData before init

    await init(level, chapter, path); // await init to complete

    // After init, state.value might be updated. Return the final state.
    // If init sets state to AsyncError, this return might be overridden.
    // The build method should return the initial state or result of async work.
    // The init method updates the state internally.
    return state.value ??
        initialState; // Return current state value or the initial one if somehow still null
  }

  Future<void> init(String level, String chapter, String path) async {
    final contents = await mainLessonRepository.getPathIndex(
      level: level,
      chapter: chapter,
      section: SectionType.listening,
    );
    await mainLessonRepository.saveIntro(lessonName: 'listening');
    contents.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) async {
        _paths = data;

        final result = await _userDataServiceRepository.getListeningResult(
          PronunciationScoreParams(level: level, chapter: chapter),
        );

        result.fold(
          (failure) {
            state = AsyncError(failure.message, StackTrace.current);
          },
          (data) {
            _completedPaths = data.map((e) => e.path).toSet();
          },
        );

        _mainLessonState = ref.read(mainLessonStateProvider);

        int initialPage = 0;
        if (path != 'blankpath') {
          final contentPath = utf8.decode(base64Url.decode(path));
          final idx = _paths.indexWhere(
            (element) => element.contentPath == contentPath,
          );
          if (idx != -1) {
            initialPage = idx;
          }
        } else if (_mainLessonState.fromLastCourse == true &&
            _mainLessonState.lastListening != null) {
          final idx = _paths.indexWhere(
            (element) =>
                element.contentPath == _mainLessonState.lastListening!.path,
          );
          if (idx != -1) {
            initialPage = idx;
          }
        }
        // Ensure state.value is not null before copying
        final currentVal = state.value ?? ListeningState();
        state = AsyncData(currentVal.copyWith(currentPage: initialPage));
        await initContent();
      },
    );
  }

  Future<void> initContent() async {
    if (_paths.isEmpty && state.value?.currentPage == 0) {
      // Avoid error if _paths is empty
      // Handle case where _paths might be empty, perhaps set an empty listenings list or error state
      final currentVal = state.value ?? ListeningState();
      state = AsyncData(currentVal.copyWith(listenings: []));
      return;
    }
    final contentFuture = mainLessonRepository.getListeningList(
      _paths.map((e) => e.contentPath).toList(),
    );

    final bookmarkFuture = _userDataServiceRepository.getBookmarksBySection(
      section: SectionType.listening,
    );

    final results = await Future.wait([contentFuture, bookmarkFuture]);

    final contentResult =
        results[0] as Either<AppException, List<ListeningPart>>;
    final bookmarkResult = results[1] as Either<AppException, List<Bookmark>>;

    if (contentResult.isLeft()) {
      final failure = contentResult.fold((l) => l, (r) => null);
      state = AsyncError(failure!.message, StackTrace.current);
      return;
    }
    if (bookmarkResult.isLeft()) {
      final failure = bookmarkResult.fold((l) => l, (r) => null);
      state = AsyncError(failure!.message, StackTrace.current);
      return;
    }

    List<ListeningPart> contentData =
        contentResult.fold((l) => null, (r) => r)!;
    final List<Bookmark> bookmarkData =
        bookmarkResult.fold((l) => null, (r) => r)!;

    int? initialExpandedIdx;
    if (contentData.isNotEmpty &&
        state.value!.currentPage < contentData.length && // bounds check
        contentData[state.value!.currentPage].questions.isNotEmpty) {
      final currentQuestions = contentData[state.value!.currentPage].questions;
      final firstUnanswered = currentQuestions.indexWhere(
        (q) => q.answer == null,
      );
      if (firstUnanswered != -1) {
        initialExpandedIdx = firstUnanswered;
      } else {
        // All answered or no questions
        initialExpandedIdx = null; // Nothing to expand if all answered
      }

      for (int i = 0; i < contentData.length; i++) {
        final item = contentData[i];
        final isBookmarked = bookmarkData.any(
          (bookmark) =>
              bookmark.path == _paths[i].contentPath && bookmark.isBookmarked,
        );
        contentData[i] = item.copyWith(isBookmarked: isBookmarked);
      }
    } else {
      initialExpandedIdx = null;
    }
    final currentVal = state.value ?? ListeningState();
    state = AsyncData(
      currentVal.copyWith(
        listenings: contentData,
        expandedQuestionIndex: initialExpandedIdx,
      ),
    );
    await saveLastCourse();
  }

  void selectAnswer({required int questionIndex, required String value}) {
    if (state.value == null) return; // Guard against null state
    int answerChoiceIndex = alphabetToIndex(value);

    final currentPageIndex = state.value!.currentPage;
    final listeningsList = state.value!.listenings;

    if (currentPageIndex < listeningsList.length &&
        questionIndex < listeningsList[currentPageIndex].questions.length) {
      List<ListeningPart> tempData = List.from(listeningsList);
      List<Question> tempList = List.from(tempData[currentPageIndex].questions);

      tempList[questionIndex] = tempList[questionIndex].copyWith(
        answer: value,
        isCorrect:
            tempData[currentPageIndex]
                .questions[questionIndex]
                .choices[answerChoiceIndex]
                .isCorrect,
      );
      tempData[currentPageIndex] = tempData[currentPageIndex].copyWith(
        questions: tempList,
      );

      int? nextUnansweredIndex;
      final questionsInCurrentPart = tempData[currentPageIndex].questions;
      for (int i = 0; i < questionsInCurrentPart.length; i++) {
        if (questionsInCurrentPart[i].answer == null) {
          nextUnansweredIndex = i;
          break;
        }
      }
      state = AsyncData(
        state.value!.copyWith(
          listenings: tempData,
          expandedQuestionIndex: nextUnansweredIndex,
        ),
      );
    }
  }

  void setExpandedQuestion(int? questionIndexToExpand) {
    if (state.value == null) return;

    int? newExpandedIndex;

    if (questionIndexToExpand != null) {
      final currentQuestions =
          state.value!.listenings[state.value!.currentPage].questions;
      if (questionIndexToExpand >= 0 &&
          questionIndexToExpand < currentQuestions.length) {
        // Only allow expanding if not answered
        if (currentQuestions[questionIndexToExpand].answer == null) {
          newExpandedIndex =
              (state.value!.expandedQuestionIndex == questionIndexToExpand)
                  ? null
                  : questionIndexToExpand;
        } else {
          // If trying to interact with an answered question, ensure nothing is expanded (or keep current if it's different)
          newExpandedIndex =
              state.value!.expandedQuestionIndex == questionIndexToExpand
                  ? null
                  : state.value!.expandedQuestionIndex;
          if (currentQuestions[questionIndexToExpand].answer != null &&
              state.value!.expandedQuestionIndex == questionIndexToExpand) {
            newExpandedIndex =
                null; // Collapse if it was an answered one that was somehow marked expanded
          }
        }
      } else {
        newExpandedIndex =
            state
                .value!
                .expandedQuestionIndex; // Keep current if index out of bounds
      }
    } else {
      // explicitly asked to collapse (questionIndexToExpand is null)
      newExpandedIndex = null;
    }

    state = AsyncData(
      state.value!.copyWith(expandedQuestionIndex: newExpandedIndex),
    );
  }

  Future<void> nextPart(BuildContext context) async {
    if (state.value == null) return;
    final currentPage = state.value!.currentPage;
    final listenings = state.value!.listenings;

    await saveLastCourse();

    if (currentPage < listenings.length - 1) {
      // Prepare the next part question index
      final nextPartQuestions = listenings[currentPage + 1].questions;
      int? nextPartExpandedIdx;
      if (nextPartQuestions.isNotEmpty) {
        final firstUnansweredInNext = nextPartQuestions.indexWhere(
          (q) => q.answer == null,
        );
        nextPartExpandedIdx =
            (firstUnansweredInNext != -1)
                ? firstUnansweredInNext
                : null; // Expand first unanswered, or null if all answered
      } else {
        nextPartExpandedIdx = null;
      }
      // Move to next part
      state = AsyncData(
        state.value!.copyWith(
          currentPage: currentPage + 1,
          isNewPart: true,
          expandedQuestionIndex: nextPartExpandedIdx,
        ),
      );

      customNav(
        context,
        RouterName.listeningMastery,
        isReplace: true,
        params: {'level': level, 'chapter': chapter, 'path': path},
      );
    } else {
      // End of all parts
      state = AsyncData(state.value!.copyWith(nextSection: true));
    }
  }

  Future<void> saveLastCourse() async {
    if (state.value == null ||
        _paths.isEmpty ||
        state.value!.currentPage >= _paths.length) {
      return;
    }

    final data = LastCourse(
      accessTime: DateTime.now().toUtc(),
      level: level, // level, chapter are build parameters
      chapter: int.parse(chapter),
      section: SectionType.listening,
      path: _paths[state.value!.currentPage].contentPath,
    );

    await _userDataServiceRepository.updateLastCourse(
      lastCourse: data,
      section: SectionType.listening,
    );
    final mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateLastListening(data);
    mainLessonStateNotifier.updateFromLastCourse(false);
  }

  String indexToAlphabet(int number) {
    switch (number) {
      case 0:
        return 'A';
      case 1:
        return 'B';
      case 2:
        return 'C';
      case 3:
        return 'D';
      default:
        return '';
    }
  }

  int alphabetToIndex(String alphabet) {
    switch (alphabet) {
      case 'A':
        return 0;
      case 'B':
        return 1;
      case 'C':
        return 2;
      case 'D':
        return 3;
      default:
        return -1;
    }
  }

  int getCorrectChoiceIndex() {
    // This would need a question index if used
    // This method seems unused in the current screen logic after changes.
    // If needed, it should operate on a specific question.
    return -1;
  }

  int calculateTotalScore() {
    if (state.value == null) return 0;
    int score = 0;
    final listenings = state.value!.listenings;
    for (var page in listenings) {
      for (var question in page.questions) {
        if (question.isCorrect ?? false) {
          score++;
        }
      }
    }
    return score;
  }

  int calculateTotalScorePart() {
    if (state.value == null ||
        state.value!.listenings.isEmpty ||
        state.value!.currentPage >= state.value!.listenings.length) {
      return 0;
    }
    int correctAnswers = calculateTotalCorrectPart();
    final questionsInPart = state.value!.currentListenings.questions;
    if (questionsInPart.isEmpty) return 0;
    return (correctAnswers / questionsInPart.length * 100).round();
  }

  int calculateTotalCorrectPart() {
    if (state.value == null ||
        state.value!.listenings.isEmpty ||
        state.value!.currentPage >= state.value!.listenings.length) {
      return 0;
    }
    int score = 0;
    final questionsInPart = state.value!.currentListenings.questions;
    for (var question in questionsInPart) {
      if (question.isCorrect ?? false) {
        score++;
      }
    }
    return score;
  }

  Future<void> saveResult() async {
    if (state.value == null ||
        _paths.isEmpty ||
        state.value!.currentPage >= _paths.length) {
      return;
    }

    final result = await _userDataServiceRepository.saveLessonResult(
      level: level,
      chapter: chapter,
      section: SectionType.listening,
      result: LessonResult(
        contentOrder: _paths[state.value!.currentPage].contentOrder,
        path: _paths[state.value!.currentPage].contentPath,
        result: {
          'correct': calculateTotalCorrectPart(),
          'total': state.value!.currentListenings.questions.length,
        },
      ),
    );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        _completedPaths.add(_paths[state.value!.currentPage].contentPath);
        _chapterContentStateNotifier.updateListening(
          _paths[state.value!.currentPage],
        );
        markSectionAsCompleted();
      },
    );
  }

  Future<void> markSectionAsCompleted() async {
    // Use set operations for O(n) performance instead of O(n*m)
    // Create a set of required paths and check if it's a subset of completed paths
    final requiredPaths =
        _paths.map((pathData) => pathData.contentPath).toSet();
    final allPathsCompleted = requiredPaths.difference(_completedPaths).isEmpty;

    if (allPathsCompleted) {
      await _userDataServiceRepository.setSectionCompleted(
        level: level,
        chapter: chapter,
        section: SectionType.listening,
      );
    }
  }

  void resetNextSectionState() {
    if (state.value != null) {
      state = AsyncData(state.value!.copyWith(nextSection: false));
    }
  }

  Future<void> saveBookmark() async {
    bool isBookmarked =
        !state.value!.listenings[state.value!.currentPage].isBookmarked;

    final String partOrder =
        (_paths[state.value!.currentPage].partOrder ?? 0).toString();
    final String subpartOrder =
        (_paths[state.value!.currentPage].subpartOrder ?? 0).toString();
    final String contentOrder =
        (_paths[state.value!.currentPage].contentOrder).toString();
    final String speakingStage = '';
    //(_paths[state.value!.currentPage].speakingStage?.name ?? '');

    final String docId =
        '$level$chapter$partOrder$subpartOrder$contentOrder$speakingStage';
    final result = await _userDataServiceRepository.saveBookmark(
      section: SectionType.listening,
      content: Bookmark(
        docId: docId,
        path: _paths[state.value!.currentPage].contentPath,
        isBookmarked: isBookmarked,
      ),
    );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        var listenings = state.value!.listenings;
        listenings = List.from(listenings);
        listenings[state.value!.currentPage] = listenings[state
                .value!
                .currentPage]
            .copyWith(isBookmarked: isBookmarked);
        // Update the current state with the new bookmark status
        state = AsyncData(state.value!.copyWith(listenings: listenings));

        _paths[state.value!.currentPage] = _paths[state.value!.currentPage]
            .copyWith(isBookmarked: isBookmarked);
        _chapterContentStateNotifier.updateListening(
          _paths[state.value!.currentPage],
        );
      },
    );
  }
}

class UnauthorizedException implements Exception {
  const UnauthorizedException(this.message);
  final String message;
}

final networkRoundTripTime = 2.seconds;
