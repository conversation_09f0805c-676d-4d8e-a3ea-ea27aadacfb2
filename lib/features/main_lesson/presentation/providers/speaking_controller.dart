import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/main_lesson_state.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';

part 'speaking_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
@riverpod
class SpeakingController extends _$SpeakingController {
  late MainLessonRepository mainLessonRepository;
  late UserDataServiceRepository _userDataServiceRepository;
  List<ContentIndexData> paths = [];
  Set<String> _stageOneCompletedPaths = {};
  Set<String> _stageTwoCompletedPaths = {};
  Set<String> _stageThreeCompletedPaths = {};
  late MainLessonState _mainLessonState;
  late ChapterContentStateNotifier _chapterContentStateNotifier;

  @override
  FutureOr<SpeakingState> build(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  ) async {
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    _userDataServiceRepository = ref.watch(userDataServiceProvider);
    _chapterContentStateNotifier = ref.watch(
      chapterContentStateProvider.notifier,
    );

    await mainLessonRepository.isIntro(lessonName: 'speaking').then((val) {
      val.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          // isIntro value is handled in the init method if needed
        },
      );
    });

    // For result screen (stage3), we need to ensure aggregate results are calculated
    // before returning the initial state to prevent UI flicker
    if (stage == SpeakingStage.stage3) {
      // Initialize with a temporary state to prevent null access
      state = AsyncData(SpeakingState());
      await init(level, chapter, path, stage);

      // Calculate aggregate results before returning the state
      await calculateAgregate(SpeakingStage.stage2);
      await calculateAgregate(SpeakingStage.stage3);
    } else {
      // For other stages, initialize normally
      state = AsyncData(SpeakingState());
      await init(level, chapter, path, stage);
    }

    return state.value ?? SpeakingState();
  }

  Future<void> init(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  ) async {
    final contents = await mainLessonRepository.getPathIndex(
      level: level,
      chapter: chapter,
      section: SectionType.speaking,
    );
    await mainLessonRepository.saveIntro(lessonName: 'speaking');
    contents.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) async {
        paths = data;

        final speakingResultsFutures = [
          _userDataServiceRepository.getSpeakingResult(
            level: level,
            chapter: chapter,
            stage: SpeakingStage.stage1,
          ),
          _userDataServiceRepository.getSpeakingResult(
            level: level,
            chapter: chapter,
            stage: SpeakingStage.stage2,
          ),
          _userDataServiceRepository.getSpeakingResult(
            level: level,
            chapter: chapter,
            stage: SpeakingStage.stage3,
          ),
        ];

        final speakingResults = await Future.wait(speakingResultsFutures);

        final completedPaths =
            speakingResults.map((result) {
              return result.fold((failure) {
                state = AsyncError(failure.message, StackTrace.current);
                return null;
              }, (data) => data.map((e) => e.path).toSet());
            }).toList();

        if (completedPaths.contains(null)) {
          return;
        }

        _stageOneCompletedPaths = completedPaths[0]!;
        _stageTwoCompletedPaths = completedPaths[1]!;
        _stageThreeCompletedPaths = completedPaths[2]!;

        _mainLessonState = ref.read(mainLessonStateProvider);
        if (_mainLessonState.fromLastCourse == true || path != 'blankpath') {
          state = AsyncData(state.value!.copyWith(showStageOnboarding: false));
        } else {
          state = AsyncData(state.value!.copyWith(showStageOnboarding: true));
        }

        if (_mainLessonState.fromLastCourse == false && path != 'blankpath') {
          final contentPath = utf8.decode(base64Url.decode(path));
          final idx = paths.indexWhere(
            (element) => element.contentPath == contentPath,
          );
          if (idx != -1) {
            state = AsyncData(
              state.value!.copyWith(
                selectedIndex: idx,
                speakingStage: stage ?? SpeakingStage.stage1,
              ),
            );
          }
        } else if (_mainLessonState.fromLastCourse == true &&
            _mainLessonState.lastSpeaking != null) {
          final idx = paths.indexWhere(
            (element) =>
                element.contentPath == _mainLessonState.lastSpeaking!.path,
          );
          if (idx != -1) {
            state = AsyncData(
              state.value!.copyWith(
                selectedIndex: idx,
                speakingStage: _mainLessonState.lastSpeaking!.speakingStage,
              ),
            );
          }
        }
        await initContent();
      },
    );
  }

  Future<void> initContent() async {
    final contentFuture = mainLessonRepository.getSpeakingList(
      paths.map((e) => e.contentPath).toList(),
    );

    final bookmarkFuture = _userDataServiceRepository.getBookmarksBySection(
      section: SectionType.speaking,
    );

    final results = await Future.wait([contentFuture, bookmarkFuture]);

    final contentResult =
        results[0] as Either<AppException, List<SpeakingPart>>;
    final bookmarkResult = results[1] as Either<AppException, List<Bookmark>>;

    if (contentResult.isLeft()) {
      final failure = contentResult.fold((l) => l, (r) => null);
      state = AsyncError(failure!.message, StackTrace.current);
      return;
    }
    if (bookmarkResult.isLeft()) {
      final failure = bookmarkResult.fold((l) => l, (r) => null);
      state = AsyncError(failure!.message, StackTrace.current);
      return;
    }

    List<SpeakingPart> contentData = contentResult.fold((l) => null, (r) => r)!;
    final List<Bookmark> bookmarkData =
        bookmarkResult.fold((l) => null, (r) => r)!;

    for (int i = 0; i < contentData.length; i++) {
      final item = contentData[i];
      final isBookmarked = bookmarkData.any(
        (bookmark) =>
            bookmark.path == paths[i].contentPath && bookmark.isBookmarked,
      );
      contentData[i] = item.copyWith(isBookmarked: isBookmarked);
    }

    state = AsyncData(state.value!.copyWith(speakings: contentData));

    if (contentData.isNotEmpty) {
      if (state.value?.speakingStage == SpeakingStage.stage2) {
        setQAActive(session: SpeakingSessionType.question);
      } else {
        setQAActive(session: SpeakingSessionType.question, isListening: true);
      }
      saveLastCourse();
    }
  }

  void setQAActive({
    SpeakingSessionType session = SpeakingSessionType.question,
    bool isListening = false,
  }) {
    List<SpeakingPart> tempData = List.from(state.value!.speakings);
    tempData[state.value!.selectedIndex] = tempData[state.value!.selectedIndex]
        .copyWith(
          question: tempData[state.value!.selectedIndex].question.copyWith(
            isActive: session == SpeakingSessionType.question ? true : false,
          ),
          answer: tempData[state.value!.selectedIndex].answer.copyWith(
            isActive: session == SpeakingSessionType.answer ? true : false,
          ),
        );
    state = AsyncData(
      state.value!.copyWith(speakings: tempData, isListening: isListening),
    );
  }

  Future<void> uploadAudio({required String path}) async {
    state = AsyncData(state.value!.copyWith(audioPath: AudioPath(path: path)));
    checkPronunciation();
  }

  Future<void> checkPronunciation() async {
    String text = '';
    switch (state.value?.speakingStage) {
      case SpeakingStage.stage2:
        text = state.value!.speakings[state.value!.selectedIndex].question.text;
        break;
      case SpeakingStage.stage3:
        text = state.value!.speakings[state.value!.selectedIndex].answer.text;
        break;
      default:
    }
    state = AsyncData(state.value!.copyWith(isLoading: true));
    final result = await mainLessonRepository.checkPronunciation(
      audio: state.value!.audioPath!,
      text: text,
    );
    result.fold(
      (failure) {
        state = AsyncData(
          state.value!.copyWith(isLoading: false, response: null),
        );
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) async {
        state = AsyncData(
          state.value!.copyWith(response: data, isLoading: false),
        );

        await saveResult();
      },
    );
  }

  void changeStage(SpeakingStage stage) {
    state = AsyncData(
      state.value!.copyWith(
        speakingStage: stage,
        response: null,
        nextSection: false,
        selectedIndex: 0,
      ),
    );
    if (state.value?.speakingStage == SpeakingStage.stage2) {
      setQAActive(isListening: false);
    } else {
      setQAActive(isListening: true);
    }
  }

  void nextStage() {
    if (state.value?.speakingStage == SpeakingStage.stage1 &&
        state.value!.nextSection) {
      changeStage(SpeakingStage.onboardingStage2);
    } else if (state.value?.speakingStage == SpeakingStage.stage2 &&
        state.value!.nextSection) {
      changeStage(SpeakingStage.onboardingStage3);
    }
  }

  Future<void> prevQuestion() async {
    if (state.value!.selectedIndex > 0) {
      state = AsyncData(
        state.value!.copyWith(
          selectedIndex: state.value!.selectedIndex - 1,
          response: null,
          // stageTalking: 'stage 1',
        ),
      );
    }
  }

  Future<void> nextQuestion(BuildContext context) async {
    SpeakingStage speakingStage = state.value!.speakingStage;
    if (state.value!.selectedIndex < state.value!.speakings.length - 1) {
      if (speakingStage == SpeakingStage.stage1) {
        saveStageOne();
      }

      state = AsyncData(
        state.value!.copyWith(
          selectedIndex: state.value!.selectedIndex + 1,
          response: null,
          // stageTalking: 'stage 1',
        ),
      );
      if (speakingStage == SpeakingStage.stage2) {
        setQAActive(session: SpeakingSessionType.question);
      } else {
        setQAActive(session: SpeakingSessionType.question, isListening: true);
      }
      if (speakingStage != SpeakingStage.stage1) {
        // Save the current state before navigation
        await saveLastCourse();
        // Set fromLastCourse to true so the next screen uses lastSpeaking data
        final mainLessonStateNotifier = ref.watch(
          mainLessonStateProvider.notifier,
        );
        mainLessonStateNotifier.updateFromLastCourse(true);

        customNav(
          context,
          RouterName.speakingArenaStage,
          isReplace: true,
          params: {
            'level': level,
            'chapter': chapter,
            'path': path,
            'stage': state.value!.speakingStage.name,
          },
        );
      } else {
        saveLastCourse();
      }
    } else {
      state = AsyncData(state.value!.copyWith(nextSection: true));
      if (state.value?.speakingStage == SpeakingStage.stage3) {
        await calculateAgregate(SpeakingStage.stage2);
        await calculateAgregate(SpeakingStage.stage3);
        customNav(
          context,
          RouterName.speakingArenaResult,
          isReplace: true,
          params: {
            'level': level,
            'chapter': chapter,
            'path': path,
            'stage': state.value!.speakingStage.name,
          },
        );
      } else {
        saveStageOne();
        nextStage();
        customNav(
          context,
          RouterName.speakingArenaStage,
          isReplace: true,
          params: {
            'level': level,
            'chapter': chapter,
            'path': path,
            'stage': state.value!.speakingStage.name,
          },
        );
      }
    }
  }

  Future<void> saveLastCourse() async {
    final data = LastCourse(
      accessTime: DateTime.now().toUtc(),
      level: level,
      chapter: int.parse(chapter),
      section: SectionType.speaking,
      path: paths[state.value!.selectedIndex].contentPath,
      speakingStage: state.value!.speakingStage,
    );

    await _userDataServiceRepository.updateLastCourse(
      lastCourse: data,
      section: SectionType.speaking,
    );
    final mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateLastSpeaking(data);
    mainLessonStateNotifier.updateFromLastCourse(false);
  }

  Future<void> calculateAgregate(SpeakingStage stage) async {
    final result = await _userDataServiceRepository.calculateSpeakingResult(
      level: level,
      chapter: chapter,
      stage: stage,
    );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        if (stage == SpeakingStage.stage2) {
          state = AsyncData(state.value!.copyWith(resultStage2: data));
        } else if (stage == SpeakingStage.stage3) {
          state = AsyncData(state.value!.copyWith(resultStage3: data));
        }
      },
    );
  }

  Future<void> saveResult() async {
    final result = await _userDataServiceRepository.saveLessonResult(
      level: level,
      chapter: chapter,
      section: SectionType.speaking,
      result: LessonResult(
        contentOrder: paths[state.value!.selectedIndex].contentOrder,
        speakingStage: state.value!.speakingStage,
        path: paths[state.value!.selectedIndex].contentPath,
        result: {
          'accuracyScore': state.value!.response!.accuracyScore,
          'fluencyScore': state.value!.response!.fluencyScore,
          'prosodyScore': state.value!.response!.prosodyScore,
          'completenessScore': state.value!.response!.completenessScore,
          'pronScore': state.value!.response!.pronScore,
        },
      ),
    );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        if (state.value!.speakingStage == SpeakingStage.stage2) {
          _stageTwoCompletedPaths.add(
            paths[state.value!.selectedIndex].contentPath,
          );
        } else if (state.value!.speakingStage == SpeakingStage.stage3) {
          _stageThreeCompletedPaths.add(
            paths[state.value!.selectedIndex].contentPath,
          );
        }
        updateHasResult();
        markSectionAsCompleted();
      },
    );
  }

  Future<void> saveStageOne() async {
    final result = await _userDataServiceRepository.saveLessonResult(
      level: level,
      chapter: chapter,
      section: SectionType.speaking,
      result: LessonResult(
        contentOrder: paths[state.value!.selectedIndex].contentOrder,
        speakingStage: SpeakingStage.stage1,
        path: paths[state.value!.selectedIndex].contentPath,
        result: {
          'accuracyScore': 0,
          'fluencyScore': 0,
          'prosodyScore': 0,
          'completenessScore': 0,
          'pronScore': 0,
        },
      ),
    );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        _stageOneCompletedPaths.add(
          paths[state.value!.selectedIndex].contentPath,
        );
        updateHasResult();
        markSectionAsCompleted();
      },
    );
  }

  Future<void> markSectionAsCompleted() async {
    // Use set operations for O(n) performance instead of O(n*m) for each stage
    // Create a set of required paths and check if all stages have completed all paths
    final requiredPaths = paths.map((pathData) => pathData.contentPath).toSet();

    final stageOneComplete =
        requiredPaths.difference(_stageOneCompletedPaths).isEmpty;
    final stageTwoComplete =
        requiredPaths.difference(_stageTwoCompletedPaths).isEmpty;
    final stageThreeComplete =
        requiredPaths.difference(_stageThreeCompletedPaths).isEmpty;

    if (stageOneComplete && stageTwoComplete && stageThreeComplete) {
      await _userDataServiceRepository.setSectionCompleted(
        level: level,
        chapter: chapter,
        section: SectionType.speaking,
      );
    }
  }

  void updateHasResult() {
    _chapterContentStateNotifier.updateSpeaking(
      paths[state.value!.selectedIndex],
      state.value!.speakingStage,
    );
  }

  void clearResponse() {
    state = AsyncData(state.value!.copyWith(response: null));
  }

  Future<void> saveBookmark() async {
    bool isBookmarked =
        !state.value!.speakings[state.value!.selectedIndex].isBookmarked;

    final String partOrder =
        (paths[state.value!.selectedIndex].partOrder ?? 0).toString();
    final String subpartOrder =
        (paths[state.value!.selectedIndex].subpartOrder ?? 0).toString();
    final String contentOrder =
        (paths[state.value!.selectedIndex].contentOrder).toString();
    final String speakingStage = '';
    //(_paths[state.value!.currentPage].speakingStage?.name ?? '');

    final String docId =
        '$level$chapter$partOrder$subpartOrder$contentOrder$speakingStage';
    final result = await _userDataServiceRepository.saveBookmark(
      section: SectionType.speaking,
      content: Bookmark(
        docId: docId,
        path: paths[state.value!.selectedIndex].contentPath,
        isBookmarked: isBookmarked,
      ),
    );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        var speakings = state.value!.speakings;
        speakings = List.from(speakings);
        speakings[state.value!.selectedIndex] = speakings[state
                .value!
                .selectedIndex]
            .copyWith(isBookmarked: isBookmarked);
        // Update the current state with the new bookmark status
        state = AsyncData(state.value!.copyWith(speakings: speakings));
        paths[state.value!.selectedIndex] = paths[state.value!.selectedIndex]
            .copyWith(isBookmarked: isBookmarked);
        _chapterContentStateNotifier.updateSpeaking(
          paths[state.value!.selectedIndex],
          state.value!.speakingStage,
        );
      },
    );
  }
}

/// Simple mock of a 401 exception
class UnauthorizedException implements Exception {
  const UnauthorizedException(this.message);
  final String message;
}

/// Mock of the duration of a network request
final networkRoundTripTime = 2.seconds;
