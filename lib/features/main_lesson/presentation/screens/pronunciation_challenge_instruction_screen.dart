import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/desc_with_image.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/next_button.dart';
import 'package:selfeng/shared/domain/models/models.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class PronunciationChallengeInstructionScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  const PronunciationChallengeInstructionScreen({
    super.key,
    required this.level,
    required this.chapter,
  });

  @override
  ConsumerState<PronunciationChallengeInstructionScreen> createState() =>
      _PronunciationChallengeInstructionScreenState();
}

class _PronunciationChallengeInstructionScreenState
    extends ConsumerState<PronunciationChallengeInstructionScreen>
    with TickerProviderStateMixin {
  late List<DefaultModel> _listItem;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
  }

  void init() {
    _isInitialized = true;
    _listItem = [
      DefaultModel(
        title: '1',
        image: '$assetImageMainLesson/pronunciation_challenge/CM4.png',
        child: DescWithImage(
          prefix: context.loc.click_the_button,
          sufix: context.loc.pc_instruction_decs1,
          image: '$assetImageMainLesson/pronunciation_challenge/Speaker.png',
        ),
      ),
      DefaultModel(
        title: '2',
        image: '$assetImageMainLesson/pronunciation_challenge/CM5-Android.png',
        child: DescWithImage(
          prefix: context.loc.click_the_button,
          sufix: context.loc.pc_instruction_decs2,
          image:
              '$assetImageMainLesson/pronunciation_challenge/Recording-small Android.png',
        ),
      ),
      DefaultModel(
        title: '3',
        image: '$assetImageMainLesson/pronunciation_challenge/CM6-Android.png',
        child: DescWithImage(
          prefix: context.loc.pc_instruction_decs3a,
          sufix: context.loc.pc_instruction_decs3b,
          image:
              '$assetImageMainLesson/pronunciation_challenge/Recording-small Process Android.png',
        ),
      ),
      DefaultModel(
        title: '4',
        image: '$assetImageMainLesson/pronunciation_challenge/CM7-Android.png',
        child: DescWithImage(
          prefix: context.loc.pc_instruction_decs4a,
          sufix: context.loc.pc_instruction_decs4b,
          image: '$assetImageMainLesson/pronunciation_challenge/Loading.png',
        ),
      ),
      DefaultModel(
        title: '5',
        image:
            '$assetImageMainLesson/${context.loc.localeName == 'en' ? 'CM4.1-Android.png' : 'pronunciation_challenge/CM4-Android.png'}',
        child: DescWithImage(
          prefix: context.loc.pc_instruction_decs5a,
          sufix: context.loc.pc_instruction_decs5b,
          customItem: Container(
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 7),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xffFA3236),
                  Color(0xffD9353C),
                  Color(0xff8C1412),
                ],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              ),
              borderRadius: BorderRadius.all(Radius.circular(40)),
            ),
            child: Text(
              context.loc.next,
              style: Theme.of(
                context,
              ).textTheme.labelSmall!.copyWith(color: Colors.white),
            ),
          ),
        ),
      ),
      DefaultModel(
        title: '6',
        image:
            '$assetImageMainLesson/${context.loc.localeName == 'en' ? 'CM10.1-Android.png' : 'pronunciation_challenge/CM10-Android.png'}',
        child: DescWithImage(
          prefix: context.loc.pc_instruction_decs6a,
          sufix: context.loc.pc_instruction_decs6b,
          customItem: Container(
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 7),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(40)),
            ),
            child: Text(
              context.loc.repeat,
              style: Theme.of(
                context,
              ).textTheme.labelSmall!.copyWith(color: Color(0xff8C1412)),
            ),
          ),
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) init();

    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xffFFF2F2),
                  Color(0xffFDD8D8),
                  Color(0xffFFECEC),
                  Color(0xffFFFFFF),
                ],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView(
              children: [
                const SizedBox(height: 32),
                LinearProgressIndicator(
                  value: 1 / 1,
                  backgroundColor: const Color(0xffFFDAD2),
                  valueColor: AlwaysStoppedAnimation(
                    Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(height: 26),
                Text(
                  context.loc.how_to_answer,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 20),
                Column(
                  children:
                      _listItem
                          .map<Widget>((item) => instructionItem(item: item))
                          .toList(),
                ),
                const SizedBox(height: 130),
              ],
            ),
          ),
          NextButton(
            onTap: () {
              customNav(
                context,
                RouterName.pronunciationChallenge,
                isReplace: true,
                params: {
                  'level': widget.level,
                  'chapter': widget.chapter,
                  'path': 'blankpath',
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Container instructionItem({required DefaultModel item}) => Container(
    padding: const EdgeInsets.symmetric(vertical: 20),
    child: Row(
      children: [
        Text('${item.title}.', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(width: 12),
        Container(
          height: 90,
          width: 90,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: const LinearGradient(
              colors: [Color(0xffFE754C), Color(0xffE21F29), Color(0xffC3151F)],
              begin: Alignment.bottomLeft,
              end: Alignment.topRight,
            ),
            image: DecorationImage(
              image: AssetImage(item.image),
              fit: BoxFit.scaleDown,
            ),
          ),
        ),
        const SizedBox(width: 12),
        item.child,
      ],
    ),
  );
}
