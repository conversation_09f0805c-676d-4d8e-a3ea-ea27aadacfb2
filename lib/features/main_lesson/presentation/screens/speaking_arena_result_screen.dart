import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/speaking_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/animated_spider_matric_chart.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/incomplete_challenge.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/next_button.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/score_item.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
// Removed incorrect theme import
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added import

/// Displays the results of the Speaking Arena challenge, including scores for different stages.
///
/// This screen fetches results using the [speakingControllerProvider] and displays
/// different UI states based on the data loading status (loading, success, error).
class SpeakingArenaResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  final String stage;

  const SpeakingArenaResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
    required this.stage,
  });

  @override
  ConsumerState<SpeakingArenaResultScreen> createState() =>
      _SpeakingArenaResultScreenState();
}

class _SpeakingArenaResultScreenState
    extends ConsumerState<SpeakingArenaResultScreen>
    with TickerProviderStateMixin {
  late final AudioPlayer _bgmPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    // Sound will be played when results are ready and displayed
  }

  @override
  void dispose() {
    _bgmPlayer.dispose();
    super.dispose();
  }

  /// Plays a congratulatory sound effect if audio is enabled globally.
  Future<void> _playCongratsSound() async {
    // Read the audio toggle state
    final isAudioEnabled = ref.read(audioToggleProvider);
    // Only play if audio is enabled
    if (isAudioEnabled) {
      // Ensure the player is stopped before playing, in case of quick rebuilds
      await _bgmPlayer.stop();
      // Use try-catch for robustness, e.g., if asset is missing
      try {
        await _bgmPlayer.play(AssetSource('sounds/score.mp3'));
      } catch (e) {
        debugPrint("Error playing sound: $e");
        // Handle error appropriately, maybe log it
      }
    }
  }

  void _navigateToNext() {
    int currentChapter = int.parse(widget.chapter.trim(), radix: 10);

    bool isLastChapter =
        widget.level == 'C2' && currentChapter == 7 || currentChapter % 8 == 0;

    if (isLastChapter) {
      _navigateToCertificateScreen();
    } else {
      _navigateToNextChapter();
    }
  }

  void _navigateToNextChapter() {
    customNav(
      context,
      RouterName.chapterTitle,
      params: {
        'level': widget.level,
        'chapter': (int.parse(widget.chapter.trim(), radix: 10) + 1).toString(),
      },
      isReplace: true,
    );
  }

  void _navigateToCertificateScreen() {
    customNav(
      context,
      RouterName.certificateNotification,
      params: {'level': widget.level},
      isReplace: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Define the provider instance for this specific screen context
    final provider = speakingControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
      SpeakingStage.values.byName(widget.stage),
    );
    // Watch the state from the provider
    final asyncState = ref.watch(provider);
    // Get the notifier for triggering actions (like showing dialog)
    // Use read here as we only need it for the button callback, not for rebuilding
    final viewModel = ref.read(provider.notifier);

    return Scaffold(
      body: Container(
        // Apply a background gradient
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFF2F2),
              Color(0xffFDD8D8),
              Color(0xffFFECEC),
              Color(0xffFFFFFF),
            ],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
          ),
        ),
        // Use AsyncValue.when for idiomatic state handling
        child: asyncState.when(
          // Loading State: Show a centered progress indicator
          loading:
              () => const Center(child: CircularProgressIndicator.adaptive()),
          // Error State: Show a centered error message
          error:
              (error, stackTrace) => Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    // Display specific error or a generic message
                    // Using a generic fallback as 'something_went_wrong' key was invalid
                    error.toString(), // Display the actual error object
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ),
          // Success State: Build the main result UI using the unwrapped data
          data: (state) => _buildSuccessContent(context, state, viewModel),
        ),
      ),
    );
  }

  /// Builds the main content area when the data fetch is successful.
  Widget _buildSuccessContent(
    BuildContext context,
    SpeakingState state, // Now receives the unwrapped SpeakingState
    SpeakingController viewModel,
  ) {
    // Ensure results are not null before proceeding
    final resultStage2 = state.resultStage2;
    final resultStage3 = state.resultStage3;
    final pathLength = viewModel.paths.length; // Cache path length

    // Check if both stages are complete based on data count matching path length
    // Added null checks for safety
    final bool isStage2Complete =
        resultStage2 != null && resultStage2.dataCount == pathLength;
    final bool isStage3Complete =
        resultStage3 != null && resultStage3.dataCount == pathLength;

    // Check if we're still in the initial loading state where aggregate results haven't been calculated yet
    // This prevents the flicker by showing loading instead of incomplete challenge during initialization
    final bool isInitializing =
        widget.stage == 'stage3' &&
        (resultStage2 == null || resultStage3 == null) &&
        pathLength > 0;

    // If we're still initializing (waiting for aggregate calculations), show loading
    if (isInitializing) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    // If both stages are complete, show the results tab view
    if (isStage2Complete && isStage3Complete) {
      // Provide default scores if null, although the check above should prevent this
      final stage2Score = resultStage2;
      final stage3Score = resultStage3;

      // Play congratulatory sound only when results are fully loaded and displayed
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _playCongratsSound();
      });

      return DefaultTabController(
        length: 2, // Two tabs for Stage II and Stage III
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch, // Stretch header
              children: [
                const SizedBox(height: 82), // Top padding
                _buildEvaluationHeader(context),
                const SizedBox(height: 24), // Reduced spacing
                _buildTabBar(context),
                // Use Expanded to allow TabBarView to take remaining space
                Expanded(
                  child: _buildTabBarView(context, stage2Score, stage3Score),
                ),
                const SizedBox(height: 80), // Space for the bottom button
              ],
            ),
            // Position the 'Next' button at the bottom
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                // Add padding to avoid overlap with system navigation
                padding: const EdgeInsets.only(bottom: 20.0),
                child: NextButton(onTap: () => _navigateToNext()),
              ),
            ),
          ],
        ),
      );
    } else {
      // If stages are not complete, show the incomplete challenge widget
      return const IncompleteChallenge(
        assetImagePath: 'assets/images/main_lesson/notcomplete2.png',
      );
    }
  }

  /// Builds the header section displaying "Evaluation Results".
  Widget _buildEvaluationHeader(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xff682000), Color(0xff490206)],
          begin: Alignment.bottomLeft,
          end: Alignment.topRight,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ), // Adjusted padding
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        context.loc.evaluation_results,
        textAlign: TextAlign.center, // Center text
        style: Theme.of(
          context,
        ).textTheme.headlineMedium?.copyWith(color: Colors.white),
      ),
    );
  }

  /// Builds the TabBar for switching between Stage II and Stage III results.
  Widget _buildTabBar(BuildContext context) {
    return TabBar(
      indicatorSize: TabBarIndicatorSize.tab,
      dividerColor: Colors.transparent, // No divider line
      labelStyle:
          Theme.of(context).textTheme.titleMedium, // Style for selected tab
      unselectedLabelStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .6),
      ), // Style for unselected tabs
      tabs: <Widget>[
        Tab(text: '${context.loc.stage} II'),
        Tab(text: '${context.loc.stage} III'),
      ],
    );
  }

  /// Builds the TabBarView containing the result details for each stage.
  Widget _buildTabBarView(
    BuildContext context,
    SpeakingAgregateScore resultStage2,
    SpeakingAgregateScore resultStage3,
  ) {
    // Use MediaQuery to constrain height if needed, but Expanded is often better
    return TabBarView(
      children: <Widget>[
        // Result view for Stage II
        _SpeakingResultDetailsView(
          score: resultStage2, // Pass the non-null score
        ),
        // Result view for Stage III
        _SpeakingResultDetailsView(
          score: resultStage3, // Pass the non-null score
        ),
      ],
    );
  }
}

/// A stateless widget to display the detailed results for a single speaking stage.
class _SpeakingResultDetailsView extends StatelessWidget {
  final SpeakingAgregateScore score;

  const _SpeakingResultDetailsView({required this.score});

  /// Calculates a rounded score based on the total score value and data count.
  /// Returns 0 if dataCount is zero or scoreValue is invalid.
  int _calculateScore(double scoreValue, int dataCount) {
    if (dataCount == 0 || scoreValue.isNaN || scoreValue.isInfinite) {
      return 0;
    }
    // Calculate the average score and round it to the nearest integer.
    return (scoreValue / dataCount).round();
  }

  /// Determines feedback text based on the pronunciation score.
  String _getFeedbackText(BuildContext context, int pronScore) {
    if (pronScore >= 90) return context.loc.impressive_work;
    if (pronScore >= 80) return context.loc.bravo;
    if (pronScore >= 65) return context.loc.getting_closer;
    if (pronScore >= 50) return context.loc.tackling_a_tough_one;
    if (pronScore >= 35) return context.loc.interesting_attempt;
    if (pronScore >= 20) return context.loc.not_quite_there_yet;
    return context.loc.keep_practicing;
  }

  @override
  Widget build(BuildContext context) {
    // Calculate individual scores for clarity
    final pronScore = _calculateScore(score.pronScore, score.dataCount);
    final accuracyScore = _calculateScore(score.accuracyScore, score.dataCount);
    final fluencyScore = _calculateScore(score.fluencyScore, score.dataCount);
    final rhythmScore = _calculateScore(
      score.prosodyScore,
      score.dataCount,
    ); // Renamed for clarity
    final completenessScore = _calculateScore(
      score.completenessScore,
      score.dataCount,
    );

    return SingleChildScrollView(
      // Ensure content scrolls if it overflows
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center, // Center content
        children: [
          // Feedback text and overall pronunciation score
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                // Allow text to wrap if needed
                child: Text(
                  _getFeedbackText(context, pronScore),
                  style: Theme.of(context).textTheme.headlineSmall,
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(width: 16), // Spacing
              ScoreItem(
                score: pronScore,
                reverse: true,
              ), // Display score visually
            ],
          ),
          const SizedBox(height: 30), // Spacing before chart
          // Spider chart visualizing detailed metrics
          AnimatedSpiderMetricsChart(
            accuracy: accuracyScore,
            fluency: fluencyScore,
            rhythm: rhythmScore,
            completeness: completenessScore,
          ),
          const SizedBox(height: 30), // Bottom padding
        ],
      ),
    );
  }
}
