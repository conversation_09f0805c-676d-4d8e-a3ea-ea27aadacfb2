import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class RecordingErrorDialog extends StatelessWidget {
  final String? message;
  final VoidCallback? onClose;

  const RecordingErrorDialog({Key? key, this.message, this.onClose})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Align(
          alignment: Alignment.topRight,
          child: GestureDetector(
            onTap: () => context.pop(),
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xffD0C4C2),
              ),
              child: Icon(Icons.close, size: 20),
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          padding: EdgeInsets.symmetric(vertical: 36, horizontal: 16),
          margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xffC00017),
                ),
                width: MediaQuery.of(context).size.width - 72,
                height: 72,
                margin: const EdgeInsets.symmetric(horizontal: 37),
                padding: const EdgeInsets.symmetric(
                  vertical: 11,
                  horizontal: 16.5,
                ),
                child: Icon(
                  Icons.mic_off_rounded,
                  size: 48,
                  color: Colors.white,
                ),
              ),
              Text(
                context.loc.recording_error,
                textAlign: TextAlign.center,
                style: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(color: Color(0xff655C5B)),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
