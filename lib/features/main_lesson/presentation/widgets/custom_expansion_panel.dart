import 'package:flutter/material.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart'; // For Question, Choice
import 'package:selfeng/features/main_lesson/presentation/widgets/select_answer.dart'; // For SelectAnswer

class CustomExpansionPanel extends StatelessWidget {
  final Question question;
  final bool isExpanded;
  final bool isAnswered;
  final bool? isCorrect; // Nullable if not answered
  final VoidCallback onHeaderTap;
  final Function(String) onChoiceSelected;
  final String Function(int) indexToAlphabet;

  const CustomExpansionPanel({
    super.key,
    required this.question,
    required this.isExpanded,
    required this.isAnswered,
    this.isCorrect,
    required this.onHeaderTap,
    required this.onChoiceSelected,
    required this.indexToAlphabet,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        // Header
        Expanded(
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 20.0,
              vertical: 8.0,
            ),
            title: Text(
              '${question.order}. ${question.question}',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: isAnswered ? Colors.black54 : Colors.black87,
              ),
            ),
            // trailing: AnimatedSwitcher(
            //   duration: const Duration(
            //     milliseconds: 400,
            //   ), // Duration for the switch animation
            //   // switchInCurve: Curves.linear, // Default, good for custom curve in transitionBuilder
            //   // switchOutCurve: Curves.linear, // Default for fade-out of old child
            //   transitionBuilder: (Widget child, Animation<double> animation) {
            //     // Check the key of the incoming child to determine the animation type
            //     final Key? childKey = child.key;
            //     bool isAnswerFeedbackIcon =
            //         childKey == const ValueKey('icon_correct') ||
            //         childKey == const ValueKey('icon_incorrect');

            //     if (isAnswerFeedbackIcon) {
            //       // "Pop" animation for check/cancel icons (when an answer is chosen)
            //       return ScaleTransition(
            //         scale: CurvedAnimation(
            //           parent:
            //               animation, // Animation from AnimatedSwitcher (0.0 to 1.0)
            //           curve: Curves.elasticOut, // Bouncy pop effect
            //         ).drive(
            //           Tween<double>(begin: 0.0, end: 1.0),
            //         ), // Scale from 0% to 100%
            //         child: child,
            //       );
            //     } else {
            //       // Default fade transition for expand/collapse icons
            //       return FadeTransition(opacity: animation, child: child);
            //     }
            //   },
            //   child: _buildTrailingIcon(
            //     context,
            //   ), // The key of this child is crucial
            // ),
            onTap:
                isAnswered
                    ? null
                    : onHeaderTap, // Only allow tap if not answered
          ),
        ),
        // Body (conditionally visible and animated)
        AnimatedSize(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: Container(
            // The child of AnimatedSize must not be const if its contents change
            // Only show body if expanded AND not answered
            child:
                (isExpanded && !isAnswered)
                    ? Padding(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                        bottom: 20,
                        top:
                            0, // No top padding as header usually provides enough space
                      ),
                      child: Column(
                        children:
                            question.choices.asMap().entries.map((choiceEntry) {
                              int choiceIndex = choiceEntry.key;
                              Choice choice = choiceEntry.value;
                              final choiceLabel = indexToAlphabet(choiceIndex);
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 12),
                                child: SelectAnswer(
                                  title: choice.text ?? '',
                                  number: choiceLabel,
                                  value: choiceLabel,
                                  groupValue:
                                      question
                                          .answer, // This comes from the Question model
                                  onTap: (val) {
                                    onChoiceSelected(val as String);
                                  },
                                ),
                              );
                            }).toList(),
                      ),
                    )
                    : const SizedBox.shrink(), // Use SizedBox.shrink when not expanded or answered
          ),
        ),
      ],
    );
  }

  Widget _buildTrailingIcon(BuildContext context) {
    if (isAnswered) {
      // Use const ValueKey for better performance and to ensure AnimatedSwitcher detects changes correctly.
      return isCorrect == true
          ? const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 28,
            key: ValueKey('icon_correct'),
          )
          : const Icon(
            Icons.cancel,
            color: Colors.red,
            size: 28,
            key: ValueKey('icon_incorrect'),
          );
    } else {
      return isExpanded
          ? const Icon(
            Icons.expand_less,
            color: Color(0xffE82329),
            size: 28,
            key: ValueKey('icon_expanded'),
          )
          : const Icon(
            Icons.expand_more,
            color: Color(0xffE82329),
            size: 28,
            key: ValueKey('icon_collapsed'),
          );
    }
  }
}
