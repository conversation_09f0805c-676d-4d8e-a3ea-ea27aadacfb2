// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class SelectAnswer extends StatelessWidget {
  const SelectAnswer({
    super.key,
    required this.title,
    required this.icon,
    required this.value,
    required this.groupValue,
    required this.onTap,
    this.allowMultiple = false,
  });
  final String title;
  final String icon;
  final value;
  final groupValue;
  final Function onTap;
  final bool allowMultiple;
  @override
  Widget build(BuildContext context) {
    final isSelected =
        groupValue == null
            ? false
            : allowMultiple
            ? (groupValue as List).contains(value)
            : groupValue == value;

    // bool isSelected;
    // bool isOther = value == 'other';
    //
    // if (allowMultiple) {
    //   isSelected = (groupValue as List).contains(value) ||
    //       (isOther && !groupValue.contains(value));
    // } else {
    //   isSelected = groupValue == value ||
    //       (isOther && groupValue != value);
    // }

    return InkWell(
      onTap: () => onTap(value),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 9, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xffFEB5A3) : null,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: const Color(0xffE82329), width: 0.4),
        ),
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            // Image.asset(
            //   icon,
            //   fit: BoxFit.fitWidth,
            //   width: 36,
            //   height: 36,
            // ),
            CachedNetworkImage(
              imageUrl: icon,
              width: 36,
              height: 36,
              fit: BoxFit.fitWidth,
              placeholder: (context, url) => const CircularProgressIndicator(),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: MediaQuery.of(context).size.width - 149,
              child: Text(title, style: Theme.of(context).textTheme.bodyMedium),
            ),
          ],
        ),
      ),
    );
  }
}
