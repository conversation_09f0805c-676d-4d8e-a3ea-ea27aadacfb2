import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/fcm_service/domain/repositories/fcm_service_repository.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';

/// Provider for DeviceInfoPlugin
final deviceInfoProvider = Provider<DeviceInfoPlugin>((ref) {
  return DeviceInfoPlugin();
});

/// Provider for FirebaseMessaging
final firebaseMessagingProvider = Provider<FirebaseMessaging>((ref) {
  return FirebaseMessaging.instance;
});

/// Provider for FCMServiceRepository
final fcmServiceProvider = Provider<FCMServiceRepository>((ref) {
  final userDataService = ref.watch(userDataServiceProvider);
  final firebaseMessaging = ref.watch(firebaseMessagingProvider);
  final deviceInfo = ref.watch(deviceInfoProvider);

  return FCMServiceRepository(userDataService, firebaseMessaging, deviceInfo);
});
