import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_routes.dart';
import 'package:selfeng/features/authentication/presentation/providers/auth_controller.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'core_router.g.dart';

/// Exposes a [GoRouter] that uses a [Listenable] to refresh its internal state.
///
/// With Riverpod, we can't register a dependency via an Inherited Widget,
/// thus making this implementation the "leanest" possible
///
/// To sync our app state with this our router, we simply update our listenable via `ref.listen`,
/// and pass it to GoRouter's `refreshListenable`.
/// In this example, this will trigger redirects on any authentication change.
///
/// Obviously, more logic could be implemented here, but again, this is meant to be a simple example.
/// You can always build more listenables and even merge more than one into a more complex `ChangeNotifier`,
/// but that's up to your case and out of this scope.
@riverpod
GoRouter router(Ref ref) {
  final GlobalKey<NavigatorState> rootNavigatorKey =
      GlobalKey<NavigatorState>();
  final isAuth = ValueNotifier<AsyncValue<bool>>(const AsyncLoading());

  ref
    ..onDispose(isAuth.dispose) // don't forget to clean after yourselves (:
    // update the listenable, when some provider value changes
    // here, we are just interested in wheter the user's logged in
    ..listen(
      authControllerProvider.select(
        (value) => value.whenData((value) => value.isAuth),
      ),
      (_, next) {
        isAuth.value = next;
      },
    );
  final router = GoRouter(
    navigatorKey: rootNavigatorKey,
    refreshListenable: isAuth,
    initialLocation: '/',
    // debugLogDiagnostics: true,
    routes: $appRoutes,
    redirect: (context, state) async {
      if (isAuth.value.unwrapPrevious().hasError) {
        return const SplashRoute().location;
      }
      if (isAuth.value.isLoading || !isAuth.value.hasValue) {
        return const SplashRoute().location;
      }
      //
      // final auth = isAuth.value.requireValue;

      final bool auth = FirebaseAuth.instance.currentUser != null;
      final isSplash = state.uri.path == const SplashRoute().location;

      // if (isSplash) return auth ? const PronunciationChallengeOnboardingRoute().location : const OnboardingRoute().location;
      if (isSplash) {
        return auth
            ? const DashboardRoute().location
            : const OnboardingRoute().location;
      }

      final isOnboarding = state.uri.path == const OnboardingRoute().location;
      if (isOnboarding) {
        return auth
            ? const DashboardRoute().location
            : const OnboardingRoute().location;
      }

      // final isSelectLanguage =
      //     state.uri.path == const LanguageRoute(origin: 'welcome').location;

      // if (isSelectLanguage) {
      //   return auth
      //       ? const LanguageRoute(origin: 'welcome').location
      //       : const OnboardingRoute().location;
      // }

      // return null;
      return auth ? null : const OnboardingRoute().location;
    },
  );
  ref.onDispose(router.dispose); // always clean up after yourselves (:

  return router;
}
