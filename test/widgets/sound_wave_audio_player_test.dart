import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/sound_wave_audio_player.dart';

void main() {
  group('SoundWaveAudioPlayer', () {
    testWidgets('should render without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SoundWaveAudioPlayer(
              audioUrl: 'https://example.com/test.mp3',
              isEnabled: true,
            ),
          ),
        ),
      );

      // Verify that the widget renders
      expect(find.byType(SoundWaveAudioPlayer), findsOneWidget);

      // Verify that the play button is present
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);

      // Verify that the time display is present
      expect(find.text('00:00'), findsOneWidget);
    });

    testWidgets('should be disabled when isEnabled is false', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SoundWaveAudioPlayer(
              audioUrl: 'https://example.com/test.mp3',
              isEnabled: false,
            ),
          ),
        ),
      );

      // Find the play button
      final playButton = find.byIcon(Icons.play_arrow);
      expect(playButton, findsOneWidget);

      // Tap the play button (should not do anything when disabled)
      await tester.tap(playButton);
      await tester.pump();

      // The icon should still be play_arrow (not changed to pause)
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
      expect(find.byIcon(Icons.pause), findsNothing);
    });
  });
}
