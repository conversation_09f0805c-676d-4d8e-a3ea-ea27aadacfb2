import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/certificate/presentation/providers/certificate_navigation_provider.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate_level.dart';

void main() {
  group('CertificateNavigationController', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should initialize with null state', () {
      final controller = container.read(
        certificateNavigationControllerProvider,
      );
      expect(controller, isNull);
    });

    test('should set certificate data correctly', () {
      // Arrange
      final level = CertificateLevel(
        id: 'a1',
        name: 'A1',
        description: 'Beginner',
      );
      final certificates = [
        Certificate(
          id: 'cert1',
          title: 'Certificate 1',
          description: 'Test certificate 1',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert1.pdf',
          level: level,
        ),
        Certificate(
          id: 'cert2',
          title: 'Certificate 2',
          description: 'Test certificate 2',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert2.pdf',
          level: level,
        ),
      ];

      // Act
      container
          .read(certificateNavigationControllerProvider.notifier)
          .setCertificateData(level: level, certificates: certificates);

      // Assert
      final state = container.read(certificateNavigationControllerProvider);
      expect(state, isNotNull);
      expect(state!.level, equals(level));
      expect(state.certificates, equals(certificates));
      expect(state.selectedCertificateId, isNull);
    });

    test('should set certificate data with selected certificate ID', () {
      // Arrange
      final level = CertificateLevel(
        id: 'a1',
        name: 'A1',
        description: 'Beginner',
      );
      final certificates = [
        Certificate(
          id: 'cert1',
          title: 'Certificate 1',
          description: 'Test certificate 1',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert1.pdf',
          level: level,
        ),
        Certificate(
          id: 'cert2',
          title: 'Certificate 2',
          description: 'Test certificate 2',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert2.pdf',
          level: level,
        ),
      ];

      // Act
      container
          .read(certificateNavigationControllerProvider.notifier)
          .setCertificateData(
            level: level,
            certificates: certificates,
            selectedCertificateId: 'cert2',
          );

      // Assert
      final state = container.read(certificateNavigationControllerProvider);
      expect(state, isNotNull);
      expect(state!.level, equals(level));
      expect(state.certificates, equals(certificates));
      expect(state.selectedCertificateId, equals('cert2'));
    });

    test(
      'should update selected certificate ID while preserving other data',
      () {
        // Arrange
        final level = CertificateLevel(
          id: 'a1',
          name: 'A1',
          description: 'Beginner',
        );
        final certificates = [
          Certificate(
            id: 'cert1',
            title: 'Certificate 1',
            description: 'Test certificate 1',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert1.pdf',
            level: level,
          ),
          Certificate(
            id: 'cert2',
            title: 'Certificate 2',
            description: 'Test certificate 2',
            dateIssued: DateTime.now(),
            certificateUrl: 'https://example.com/cert2.pdf',
            level: level,
          ),
        ];

        // Set initial data
        container
            .read(certificateNavigationControllerProvider.notifier)
            .setCertificateData(level: level, certificates: certificates);

        // Act - Update selected certificate
        container
            .read(certificateNavigationControllerProvider.notifier)
            .setSelectedCertificate('cert2');

        // Assert
        final state = container.read(certificateNavigationControllerProvider);
        expect(state, isNotNull);
        expect(state!.level, equals(level));
        expect(state.certificates, equals(certificates));
        expect(state.selectedCertificateId, equals('cert2'));
      },
    );

    test('should clear certificate data', () {
      // Arrange
      final level = CertificateLevel(
        id: 'a1',
        name: 'A1',
        description: 'Beginner',
      );
      final certificates = [
        Certificate(
          id: 'cert1',
          title: 'Certificate 1',
          description: 'Test certificate 1',
          dateIssued: DateTime.now(),
          certificateUrl: 'https://example.com/cert1.pdf',
          level: level,
        ),
      ];

      container
          .read(certificateNavigationControllerProvider.notifier)
          .setCertificateData(level: level, certificates: certificates);

      // Act
      container
          .read(certificateNavigationControllerProvider.notifier)
          .clearCertificateData();

      // Assert
      final state = container.read(certificateNavigationControllerProvider);
      expect(state, isNull);
    });

    test('should not update selected certificate if state is null', () {
      // Act - Try to update selected certificate when state is null
      container
          .read(certificateNavigationControllerProvider.notifier)
          .setSelectedCertificate('cert1');

      // Assert
      final state = container.read(certificateNavigationControllerProvider);
      expect(state, isNull);
    });
  });
}
